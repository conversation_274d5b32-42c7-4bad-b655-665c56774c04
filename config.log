This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by DRMAA for Slurm configure 1.1.5, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  $ ./configure --prefix /home/<USER>/slurm-drmaa-1.1.5

## --------- ##
## Platform. ##
## --------- ##

hostname = 4125GS
uname -m = x86_64
uname -r = 6.1.0-31-amd64
uname -s = Linux
uname -v = #1 SMP PREEMPT_DYNAMIC Debian 6.1.128-1 (2025-02-07)

/usr/bin/uname -p = unknown
/bin/uname -X     = unknown

/bin/arch              = x86_64
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /opt/miniconda3/condabin/
PATH: /usr/local/bin/
PATH: /usr/bin/
PATH: /bin/
PATH: /usr/games/
PATH: /home/<USER>/.antigen/bundles/robbyrussell/oh-my-zsh/lib/
PATH: /home/<USER>/.antigen/bundles/Aloxaf/fzf-tab/
PATH: /home/<USER>/.fzf/bin/
PATH: /usr/local/biotools/rosetta/rosetta.source.release-371//main/source/bin/


## ----------- ##
## Core tests. ##
## ----------- ##

configure:2570: looking for aux files: config.guess config.sub ltmain.sh compile ar-lib missing install-sh
configure:2583:  trying ./scripts/
configure:2612:   ./scripts/config.guess found
configure:2612:   ./scripts/config.sub found
configure:2612:   ./scripts/ltmain.sh found
configure:2612:   ./scripts/compile found
configure:2612:   ./scripts/ar-lib found
configure:2612:   ./scripts/missing found
configure:2594:   ./scripts/install-sh found
configure:2758: checking for a BSD-compatible install
configure:2831: result: /usr/bin/install -c
configure:2842: checking whether build environment is sane
configure:2897: result: yes
configure:3056: checking for a race-free mkdir -p
configure:3100: result: /usr/bin/mkdir -p
configure:3107: checking for gawk
configure:3142: result: no
configure:3107: checking for mawk
configure:3128: found /usr/bin/mawk
configure:3139: result: mawk
configure:3150: checking whether make sets $(MAKE)
configure:3173: result: yes
configure:3203: checking whether make supports nested variables
configure:3221: result: yes
configure:3370: checking whether make supports the include directive
configure:3385: make -f confmf.GNU && cat confinc.out
this is the am__doit target
configure:3388: $? = 0
configure:3407: result: yes (GNU style)
configure:3483: checking for gcc
configure:3504: found /usr/bin/gcc
configure:3515: result: gcc
configure:3868: checking for C compiler version
configure:3877: gcc --version >&5
gcc (Debian 12.2.0-14+deb12u1) 12.2.0
Copyright (C) 2022 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:3888: $? = 0
configure:3877: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/12/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Debian 12.2.0-14+deb12u1' --with-bugurl=file:///usr/share/doc/gcc-12/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-12 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/reproducible-path/gcc-12-12.2.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/reproducible-path/gcc-12-12.2.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 12.2.0 (Debian 12.2.0-14+deb12u1) 
... rest of stderr output deleted ...
configure:3888: $? = 0
configure:3877: gcc -V >&5
gcc: error: unrecognized command-line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:3888: $? = 1
configure:3877: gcc -qversion >&5
gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc: fatal error: no input files
compilation terminated.
configure:3888: $? = 1
configure:3877: gcc -version >&5
gcc: error: unrecognized command-line option '-version'
gcc: fatal error: no input files
compilation terminated.
configure:3888: $? = 1
configure:3908: checking whether the C compiler works
configure:3930: gcc    conftest.c  >&5
configure:3934: $? = 0
configure:3984: result: yes
configure:3987: checking for C compiler default output file name
configure:3989: result: a.out
configure:3995: checking for suffix of executables
configure:4002: gcc -o conftest    conftest.c  >&5
configure:4006: $? = 0
configure:4029: result: 
configure:4051: checking whether we are cross compiling
configure:4059: gcc -o conftest    conftest.c  >&5
configure:4063: $? = 0
configure:4070: ./conftest
configure:4074: $? = 0
configure:4089: result: no
configure:4094: checking for suffix of object files
configure:4117: gcc -c   conftest.c >&5
configure:4121: $? = 0
configure:4143: result: o
configure:4147: checking whether the compiler supports GNU C
configure:4167: gcc -c   conftest.c >&5
configure:4167: $? = 0
configure:4177: result: yes
configure:4188: checking whether gcc accepts -g
configure:4209: gcc -c -g  conftest.c >&5
configure:4209: $? = 0
configure:4253: result: yes
configure:4273: checking for gcc option to enable C11 features
configure:4288: gcc  -c -g -O2  conftest.c >&5
configure:4288: $? = 0
configure:4306: result: none needed
configure:4422: checking whether gcc understands -c and -o together
configure:4445: gcc -c conftest.c -o conftest2.o
configure:4448: $? = 0
configure:4445: gcc -c conftest.c -o conftest2.o
configure:4448: $? = 0
configure:4460: result: yes
configure:4479: checking dependency style of gcc
configure:4591: result: gcc3
configure:4662: checking for ar
configure:4683: found /usr/bin/ar
configure:4694: result: ar
configure:4720: checking the archiver (ar) interface
configure:4737: gcc -c -g -O2  conftest.c >&5
configure:4737: $? = 0
configure:4740: ar cru libconftest.a conftest.o >&5
ar: `u' modifier ignored since `D' is the default (see `U')
configure:4743: $? = 0
configure:4771: result: ar
configure:4860: checking for gcc
configure:4892: result: gcc
configure:5245: checking for C compiler version
configure:5254: gcc --version >&5
gcc (Debian 12.2.0-14+deb12u1) 12.2.0
Copyright (C) 2022 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:5265: $? = 0
configure:5254: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/12/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Debian 12.2.0-14+deb12u1' --with-bugurl=file:///usr/share/doc/gcc-12/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-12 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/reproducible-path/gcc-12-12.2.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/reproducible-path/gcc-12-12.2.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 12.2.0 (Debian 12.2.0-14+deb12u1) 
... rest of stderr output deleted ...
configure:5265: $? = 0
configure:5254: gcc -V >&5
gcc: error: unrecognized command-line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:5265: $? = 1
configure:5254: gcc -qversion >&5
gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc: fatal error: no input files
compilation terminated.
configure:5265: $? = 1
configure:5254: gcc -version >&5
gcc: error: unrecognized command-line option '-version'
gcc: fatal error: no input files
compilation terminated.
configure:5265: $? = 1
configure:5269: checking whether the compiler supports GNU C
configure:5299: result: yes
configure:5310: checking whether gcc accepts -g
configure:5375: result: yes
configure:5395: checking for gcc option to enable C11 features
configure:5428: result: none needed
configure:5544: checking whether gcc understands -c and -o together
configure:5582: result: yes
configure:5601: checking dependency style of gcc
configure:5713: result: gcc3
configure:5734: checking how to run the C preprocessor
configure:5760: gcc -E  conftest.c
configure:5760: $? = 0
configure:5775: gcc -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:5775: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "DRMAA for Slurm"
| #define PACKAGE_TARNAME "slurm-drmaa"
| #define PACKAGE_VERSION "1.1.5"
| #define PACKAGE_STRING "DRMAA for Slurm 1.1.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "slurm-drmaa"
| #define VERSION "1.1.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:5802: result: gcc -E
configure:5816: gcc -E  conftest.c
configure:5816: $? = 0
configure:5831: gcc -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:5831: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "DRMAA for Slurm"
| #define PACKAGE_TARNAME "slurm-drmaa"
| #define PACKAGE_VERSION "1.1.5"
| #define PACKAGE_STRING "DRMAA for Slurm 1.1.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "slurm-drmaa"
| #define VERSION "1.1.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:5894: checking build system type
configure:5909: result: x86_64-pc-linux-gnu
configure:5929: checking host system type
configure:5943: result: x86_64-pc-linux-gnu
configure:5984: checking how to print strings
configure:6011: result: printf
configure:6032: checking for a sed that does not truncate output
configure:6102: result: /usr/bin/sed
configure:6120: checking for grep that handles long lines and -e
configure:6184: result: /usr/bin/grep
configure:6189: checking for egrep
configure:6257: result: /usr/bin/grep -E
configure:6262: checking for fgrep
configure:6330: result: /usr/bin/grep -F
configure:6366: checking for ld used by gcc
configure:6434: result: /usr/bin/ld
configure:6441: checking if the linker (/usr/bin/ld) is GNU ld
configure:6457: result: yes
configure:6469: checking for BSD- or MS-compatible name lister (nm)
configure:6524: result: /usr/bin/nm -B
configure:6664: checking the name lister (/usr/bin/nm -B) interface
configure:6672: gcc -c -g -O2  conftest.c >&5
configure:6675: /usr/bin/nm -B "conftest.o"
configure:6678: output
0000000000000000 B some_variable
configure:6685: result: BSD nm
configure:6688: checking whether ln -s works
configure:6692: result: yes
configure:6700: checking the maximum length of command line arguments
configure:6832: result: 1572864
configure:6880: checking how to convert x86_64-pc-linux-gnu file names to x86_64-pc-linux-gnu format
configure:6921: result: func_convert_file_noop
configure:6928: checking how to convert x86_64-pc-linux-gnu file names to toolchain format
configure:6949: result: func_convert_file_noop
configure:6956: checking for /usr/bin/ld option to reload object files
configure:6964: result: -r
configure:7043: checking for file
configure:7064: found /usr/bin/file
configure:7075: result: file
configure:7151: checking for objdump
configure:7172: found /usr/bin/objdump
configure:7183: result: objdump
configure:7215: checking how to recognize dependent libraries
configure:7416: result: pass_all
configure:7506: checking for dlltool
configure:7541: result: no
configure:7571: checking how to associate runtime and link libraries
configure:7599: result: printf %s\n
configure:7749: checking for archiver @FILE support
configure:7767: gcc -c -g -O2  conftest.c >&5
configure:7767: $? = 0
configure:7771: ar cr libconftest.a @conftest.lst >&5
configure:7774: $? = 0
configure:7779: ar cr libconftest.a @conftest.lst >&5
ar: conftest.o: No such file or directory
configure:7782: $? = 1
configure:7794: result: @
configure:7857: checking for strip
configure:7878: found /usr/bin/strip
configure:7889: result: strip
configure:7966: checking for ranlib
configure:7987: found /usr/bin/ranlib
configure:7998: result: ranlib
configure:8100: checking command to parse /usr/bin/nm -B output from gcc object
configure:8254: gcc -c -g -O2  conftest.c >&5
configure:8257: $? = 0
configure:8261: /usr/bin/nm -B conftest.o | /usr/bin/sed -n -e 's/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p' | /usr/bin/sed '/ __gnu_lto/d' > conftest.nm
configure:8327: gcc -o conftest -g -O2   conftest.c conftstm.o >&5
configure:8330: $? = 0
configure:8368: result: ok
configure:8415: checking for sysroot
configure:8446: result: no
configure:8453: checking for a working dd
configure:8497: result: /usr/bin/dd
configure:8501: checking how to truncate binary pipes
configure:8517: result: /usr/bin/dd bs=4096 count=1
configure:8654: gcc -c -g -O2  conftest.c >&5
configure:8657: $? = 0
configure:8854: checking for mt
configure:8875: found /usr/bin/mt
configure:8886: result: mt
configure:8909: checking if mt is a manifest tool
configure:8916: mt '-?'
configure:8924: result: no
configure:9649: checking for stdio.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9649: checking for stdlib.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9649: checking for string.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9649: checking for inttypes.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9649: checking for stdint.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9649: checking for strings.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9649: checking for sys/stat.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9649: checking for sys/types.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9649: checking for unistd.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9649: checking for sys/time.h
configure:9649: gcc -c -g -O2  conftest.c >&5
configure:9649: $? = 0
configure:9649: result: yes
configure:9674: checking for dlfcn.h
configure:9674: gcc -c -g -O2  conftest.c >&5
configure:9674: $? = 0
configure:9674: result: yes
configure:9933: checking for objdir
configure:9949: result: .libs
configure:10213: checking if gcc supports -fno-rtti -fno-exceptions
configure:10232: gcc -c -g -O2  -fno-rtti -fno-exceptions conftest.c >&5
cc1: warning: command-line option '-fno-rtti' is valid for C++/D/ObjC++ but not for C
configure:10236: $? = 0
configure:10249: result: no
configure:10613: checking for gcc option to produce PIC
configure:10621: result: -fPIC -DPIC
configure:10629: checking if gcc PIC flag -fPIC -DPIC works
configure:10648: gcc -c -g -O2  -fPIC -DPIC -DPIC conftest.c >&5
configure:10652: $? = 0
configure:10665: result: yes
configure:10694: checking if gcc static flag -static works
configure:10723: result: yes
configure:10738: checking if gcc supports -c -o file.o
configure:10760: gcc -c -g -O2  -o out/conftest2.o conftest.c >&5
configure:10764: $? = 0
configure:10786: result: yes
configure:10794: checking if gcc supports -c -o file.o
configure:10842: result: yes
configure:10875: checking whether the gcc linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:12149: result: yes
configure:12186: checking whether -lc should be explicitly linked in
configure:12195: gcc -c -g -O2  conftest.c >&5
configure:12198: $? = 0
configure:12213: gcc -shared  -fPIC -DPIC conftest.o  -v -Wl,-soname -Wl,conftest -o conftest 2\>\&1 \| /usr/bin/grep  -lc  \>/dev/null 2\>\&1
configure:12216: $? = 0
configure:12230: result: no
configure:12390: checking dynamic linker characteristics
configure:12972: gcc -o conftest -g -O2   -Wl,-rpath -Wl,/foo conftest.c  >&5
configure:12972: $? = 0
configure:13223: result: GNU/Linux ld.so
configure:13345: checking how to hardcode library paths into programs
configure:13370: result: immediate
configure:13922: checking whether stripping libraries is possible
configure:13931: result: yes
configure:13973: checking if libtool supports shared libraries
configure:13975: result: yes
configure:13978: checking whether to build shared libraries
configure:14003: result: yes
configure:14006: checking whether to build static libraries
configure:14010: result: yes
configure:14047: checking whether make sets $(MAKE)
configure:14070: result: yes
configure:14079: checking whether ln -s works
configure:14083: result: yes
configure:14114: checking whether gcc accepts -Wno-missing-field-initializers
configure:14122: gcc -c -pedantic -std=c99 -g -O2 -Wall -Wextra -Wno-missing-field-initializers  conftest.c >&5
configure:14122: $? = 0
configure:14125: result: yes
configure:14134: checking whether gcc accepts -Wno-format-zero-length
configure:14141: gcc -c -pedantic -std=c99 -g -O2 -Wno-format-zero-length  conftest.c >&5
configure:14141: $? = 0
configure:14144: result: yes
configure:14384: checking whether gcc is Clang
configure:14411: result: no
configure:14539: checking whether pthreads work with -pthread
configure:14639: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:14639: $? = 0
configure:14649: result: yes
configure:14669: checking for joinable pthread attribute
configure:14688: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:14688: $? = 0
configure:14697: result: PTHREAD_CREATE_JOINABLE
configure:14710: checking whether more special flags are required for pthreads
configure:14724: result: no
configure:14733: checking for PTHREAD_PRIO_INHERIT
configure:14750: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:14750: $? = 0
configure:14760: result: yes
configure:14888: checking for SLURM
configure:14891: checking for SLURM compile flags
configure:14907: result: -I/usr/include
configure:14910: checking for SLURM library dir
configure:14925: result: /usr/lib
configure:14940: checking for usable SLURM libraries/headers
configure:14962: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -I/usr/include  -Wl,-rpath=/usr/lib -L/usr/lib conftest.c   -lslurm  >&5
configure:14962: $? = 0
configure:14962: ./conftest
configure:14962: $? = 0
configure:14974: result: yes
configure:14977: checking for slurmdb_users_get in -lslurm
configure:15000: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -I/usr/include  -Wl,-rpath=/usr/lib -L/usr/lib conftest.c -lslurm    -lslurm  >&5
configure:15000: $? = 0
configure:15010: result: yes
configure:15022: Using slurm libraries -lslurm 
configure:15050: checking for egrep
configure:15118: result: /usr/bin/grep -E
configure:15134: checking for _Bool
configure:15134: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
configure:15134: $? = 0
configure:15134: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
conftest.c: In function 'main':
conftest.c:58:20: error: expected expression before ')' token
   58 | if (sizeof ((_Bool)))
      |                    ^
configure:15134: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "DRMAA for Slurm"
| #define PACKAGE_TARNAME "slurm-drmaa"
| #define PACKAGE_VERSION "1.1.5"
| #define PACKAGE_STRING "DRMAA for Slurm 1.1.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "slurm-drmaa"
| #define VERSION "1.1.5"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_LIBSLURM 1
| #define TIME_WITH_SYS_TIME 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((_Bool)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:15134: result: yes
configure:15143: checking for stdbool.h that conforms to C99
configure:15259: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
configure:15259: $? = 0
configure:15267: result: yes
configure:15276: checking for stddef.h
configure:15276: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
configure:15276: $? = 0
configure:15276: result: yes
configure:15282: checking for stdlib.h
configure:15282: result: yes
configure:15288: checking for string.h
configure:15288: result: yes
configure:15294: checking for strings.h
configure:15294: result: yes
configure:15300: checking for sys/time.h
configure:15300: result: yes
configure:15306: checking for unistd.h
configure:15306: result: yes
configure:15315: checking for size_t
configure:15315: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
configure:15315: $? = 0
configure:15315: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
conftest.c: In function 'main':
conftest.c:66:21: error: expected expression before ')' token
   66 | if (sizeof ((size_t)))
      |                     ^
configure:15315: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "DRMAA for Slurm"
| #define PACKAGE_TARNAME "slurm-drmaa"
| #define PACKAGE_VERSION "1.1.5"
| #define PACKAGE_STRING "DRMAA for Slurm 1.1.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "slurm-drmaa"
| #define VERSION "1.1.5"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_LIBSLURM 1
| #define TIME_WITH_SYS_TIME 1
| #define HAVE__BOOL 1
| #define HAVE_STDBOOL_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((size_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:15315: result: yes
configure:15325: checking whether struct tm is in sys/time.h or time.h
configure:15346: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
configure:15346: $? = 0
configure:15354: result: time.h
configure:15364: checking for an ANSI C-conforming const
configure:15431: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
configure:15431: $? = 0
configure:15439: result: yes
configure:15447: checking for inline
configure:15464: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
configure:15464: $? = 0
configure:15473: result: inline
configure:15491: checking for working volatile
configure:15511: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc conftest.c >&5
configure:15511: $? = 0
configure:15519: result: yes
configure:15529: checking for GNU libc compatible malloc
configure:15561: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15561: $? = 0
configure:15561: ./conftest
configure:15561: $? = 0
configure:15572: result: yes
configure:15597: checking for strftime
configure:15597: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
conftest.c:52:6: warning: conflicting types for built-in function 'strftime'; expected 'long unsigned int(char *, long unsigned int,  const char *, const void *)' [-Wbuiltin-declaration-mismatch]
   52 | char strftime ();
      |      ^~~~~~~~
conftest.c:44:1: note: 'strftime' is declared in header '<time.h>'
   43 | #include <limits.h>
   44 | #undef strftime
configure:15597: $? = 0
configure:15597: result: yes
configure:15653: checking for vprintf
configure:15653: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
conftest.c:53:6: warning: conflicting types for built-in function 'vprintf'; expected 'int(const char *, __va_list_tag *)' [-Wbuiltin-declaration-mismatch]
   53 | char vprintf ();
      |      ^~~~~~~
conftest.c:45:1: note: 'vprintf' is declared in header '<stdio.h>'
   44 | #include <limits.h>
   45 | #undef vprintf
configure:15653: $? = 0
configure:15653: result: yes
configure:15674: checking for asprintf
configure:15674: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15674: $? = 0
configure:15674: result: yes
configure:15680: checking for fstat
configure:15680: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15680: $? = 0
configure:15680: result: yes
configure:15686: checking for getcwd
configure:15686: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15686: $? = 0
configure:15686: result: yes
configure:15692: checking for gettimeofday
configure:15692: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15692: $? = 0
configure:15692: result: yes
configure:15698: checking for localtime_r
configure:15698: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15698: $? = 0
configure:15698: result: yes
configure:15704: checking for memset
configure:15704: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
conftest.c:59:6: warning: conflicting types for built-in function 'memset'; expected 'void *(void *, int,  long unsigned int)' [-Wbuiltin-declaration-mismatch]
   59 | char memset ();
      |      ^~~~~~
conftest.c:51:1: note: 'memset' is declared in header '<string.h>'
   50 | #include <limits.h>
   51 | #undef memset
configure:15704: $? = 0
configure:15704: result: yes
configure:15710: checking for mkstemp
configure:15710: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15710: $? = 0
configure:15710: result: yes
configure:15716: checking for setenv
configure:15716: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15716: $? = 0
configure:15716: result: yes
configure:15722: checking for strcasecmp
configure:15722: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15722: $? = 0
configure:15722: result: yes
configure:15728: checking for strchr
configure:15728: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
conftest.c:63:6: warning: conflicting types for built-in function 'strchr'; expected 'char *(const char *, int)' [-Wbuiltin-declaration-mismatch]
   63 | char strchr ();
      |      ^~~~~~
conftest.c:55:1: note: 'strchr' is declared in header '<string.h>'
   54 | #include <limits.h>
   55 | #undef strchr
configure:15728: $? = 0
configure:15728: result: yes
configure:15734: checking for strdup
configure:15734: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15734: $? = 0
configure:15734: result: yes
configure:15740: checking for strerror
configure:15740: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15740: $? = 0
configure:15740: result: yes
configure:15746: checking for strlcpy
configure:15746: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
/usr/bin/ld: /tmp/ccl4SLtP.o: in function `main':
/home/<USER>/slurm-drmaa-1.1.5/conftest.c:77: undefined reference to `strlcpy'
collect2: error: ld returned 1 exit status
configure:15746: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "DRMAA for Slurm"
| #define PACKAGE_TARNAME "slurm-drmaa"
| #define PACKAGE_VERSION "1.1.5"
| #define PACKAGE_STRING "DRMAA for Slurm 1.1.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "slurm-drmaa"
| #define VERSION "1.1.5"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_LIBSLURM 1
| #define TIME_WITH_SYS_TIME 1
| #define HAVE__BOOL 1
| #define HAVE_STDBOOL_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_MALLOC 1
| #define HAVE_STRFTIME 1
| #define HAVE_VPRINTF 1
| #define HAVE_ASPRINTF 1
| #define HAVE_FSTAT 1
| #define HAVE_GETCWD 1
| #define HAVE_GETTIMEOFDAY 1
| #define HAVE_LOCALTIME_R 1
| #define HAVE_MEMSET 1
| #define HAVE_MKSTEMP 1
| #define HAVE_SETENV 1
| #define HAVE_STRCASECMP 1
| #define HAVE_STRCHR 1
| #define HAVE_STRDUP 1
| #define HAVE_STRERROR 1
| /* end confdefs.h.  */
| /* Define strlcpy to an innocuous variant, in case <limits.h> declares strlcpy.
|    For example, HP-UX 11i <limits.h> declares gettimeofday.  */
| #define strlcpy innocuous_strlcpy
| 
| /* System header to define __stub macros and hopefully few prototypes,
|    which can conflict with char strlcpy (); below.  */
| 
| #include <limits.h>
| #undef strlcpy
| 
| /* Override any GCC internal prototype to avoid an error.
|    Use char because int might match the return type of a GCC
|    builtin and then its argument prototype would still apply.  */
| #ifdef __cplusplus
| extern "C"
| #endif
| char strlcpy ();
| /* The GNU C library defines this for functions which it implements
|     to always fail with ENOSYS.  Some functions are actually named
|     something starting with __ and the normal name is an alias.  */
| #if defined __stub_strlcpy || defined __stub___strlcpy
| choke me
| #endif
| 
| int
| main (void)
| {
| return strlcpy ();
|   ;
|   return 0;
| }
configure:15746: result: no
configure:15752: checking for strndup
configure:15752: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15752: $? = 0
configure:15752: result: yes
configure:15758: checking for strstr
configure:15758: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
conftest.c:67:6: warning: conflicting types for built-in function 'strstr'; expected 'char *(const char *, const char *)' [-Wbuiltin-declaration-mismatch]
   67 | char strstr ();
      |      ^~~~~~
conftest.c:59:1: note: 'strstr' is declared in header '<string.h>'
   58 | #include <limits.h>
   59 | #undef strstr
configure:15758: $? = 0
configure:15758: result: yes
configure:15764: checking for strtol
configure:15764: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15764: $? = 0
configure:15764: result: yes
configure:15770: checking for vasprintf
configure:15770: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc  conftest.c   >&5
configure:15770: $? = 0
configure:15770: result: yes
configure:15897: checking that generated files are newer than configure
configure:15903: result: done
configure:15934: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by DRMAA for Slurm config.status 1.1.5, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on 4125GS

config.status:1132: creating Makefile
config.status:1132: creating test/Makefile
config.status:1132: creating slurm_drmaa/Makefile
config.status:1132: creating config.h
config.status:1313: config.h is unchanged
config.status:1361: executing depfiles commands
config.status:1438: cd test       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1443: $? = 0
config.status:1438: cd slurm_drmaa       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1443: $? = 0
config.status:1361: executing libtool commands
configure:18151: === configuring in drmaa_utils (/home/<USER>/slurm-drmaa-1.1.5/drmaa_utils)
configure:18212: running /bin/bash ./configure --disable-option-checking '--prefix=/home/<USER>/slurm-drmaa-1.1.5'  --cache-file=/dev/null --srcdir=.

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=x86_64-pc-linux-gnu
ac_cv_c_compiler_gnu=yes
ac_cv_c_const=yes
ac_cv_c_inline=inline
ac_cv_c_volatile=yes
ac_cv_env_CC_set=
ac_cv_env_CC_value=
ac_cv_env_CFLAGS_set=
ac_cv_env_CFLAGS_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CPP_set=
ac_cv_env_CPP_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_LT_SYS_LIBRARY_PATH_set=
ac_cv_env_LT_SYS_LIBRARY_PATH_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_func_asprintf=yes
ac_cv_func_fstat=yes
ac_cv_func_getcwd=yes
ac_cv_func_gettimeofday=yes
ac_cv_func_localtime_r=yes
ac_cv_func_malloc_0_nonnull=yes
ac_cv_func_memset=yes
ac_cv_func_mkstemp=yes
ac_cv_func_setenv=yes
ac_cv_func_strcasecmp=yes
ac_cv_func_strchr=yes
ac_cv_func_strdup=yes
ac_cv_func_strerror=yes
ac_cv_func_strftime=yes
ac_cv_func_strlcpy=no
ac_cv_func_strndup=yes
ac_cv_func_strstr=yes
ac_cv_func_strtol=yes
ac_cv_func_vasprintf=yes
ac_cv_func_vprintf=yes
ac_cv_header_dlfcn_h=yes
ac_cv_header_inttypes_h=yes
ac_cv_header_stdbool_h=yes
ac_cv_header_stddef_h=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdio_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_time_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_unistd_h=yes
ac_cv_host=x86_64-pc-linux-gnu
ac_cv_lib_slurm_slurmdb_users_get=yes
ac_cv_objext=o
ac_cv_path_EGREP='/usr/bin/grep -E'
ac_cv_path_FGREP='/usr/bin/grep -F'
ac_cv_path_GREP=/usr/bin/grep
ac_cv_path_SED=/usr/bin/sed
ac_cv_path_install='/usr/bin/install -c'
ac_cv_path_lt_DD=/usr/bin/dd
ac_cv_path_mkdir=/usr/bin/mkdir
ac_cv_prog_AWK=mawk
ac_cv_prog_CPP='gcc -E'
ac_cv_prog_ac_ct_AR=ar
ac_cv_prog_ac_ct_CC=gcc
ac_cv_prog_ac_ct_FILECMD=file
ac_cv_prog_ac_ct_MANIFEST_TOOL=mt
ac_cv_prog_ac_ct_OBJDUMP=objdump
ac_cv_prog_ac_ct_RANLIB=ranlib
ac_cv_prog_ac_ct_STRIP=strip
ac_cv_prog_cc_c11=
ac_cv_prog_cc_g=yes
ac_cv_prog_cc_stdc=
ac_cv_prog_make_make_set=yes
ac_cv_struct_tm=time.h
ac_cv_type__Bool=yes
ac_cv_type_size_t=yes
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_ar_interface=ar
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes
ax_cv_PTHREAD_CLANG=no
ax_cv_PTHREAD_JOINABLE_ATTR=PTHREAD_CREATE_JOINABLE
ax_cv_PTHREAD_PRIO_INHERIT=yes
ax_cv_PTHREAD_SPECIAL_FLAGS=no
lt_cv_ar_at_file=@
lt_cv_archive_cmds_need_lc=no
lt_cv_deplibs_check_method=pass_all
lt_cv_file_magic_cmd='$MAGIC_CMD'
lt_cv_file_magic_test_file=
lt_cv_ld_reload_flag=-r
lt_cv_nm_interface='BSD nm'
lt_cv_objdir=.libs
lt_cv_path_LD=/usr/bin/ld
lt_cv_path_NM='/usr/bin/nm -B'
lt_cv_path_mainfest_tool=no
lt_cv_prog_compiler_c_o=yes
lt_cv_prog_compiler_pic='-fPIC -DPIC'
lt_cv_prog_compiler_pic_works=yes
lt_cv_prog_compiler_rtti_exceptions=no
lt_cv_prog_compiler_static_works=yes
lt_cv_prog_gnu_ld=yes
lt_cv_sharedlib_from_linklib_cmd='printf %s\n'
lt_cv_shlibpath_overrides_runpath=yes
lt_cv_sys_global_symbol_pipe='/usr/bin/sed -n -e '\''s/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p'\'' | /usr/bin/sed '\''/ __gnu_lto/d'\'''
lt_cv_sys_global_symbol_to_c_name_address='/usr/bin/sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_c_name_address_lib_prefix='/usr/bin/sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(lib.*\)$/  {"\1", (void *) \&\1},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"lib\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_cdecl='/usr/bin/sed -n -e '\''s/^T .* \(.*\)$/extern int \1();/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/extern char \1;/p'\'''
lt_cv_sys_global_symbol_to_import=
lt_cv_sys_max_cmd_len=1572864
lt_cv_to_host_file_cmd=func_convert_file_noop
lt_cv_to_tool_file_cmd=func_convert_file_noop
lt_cv_truncate_bin='/usr/bin/dd bs=4096 count=1'

## ----------------- ##
## Output variables. ##
## ----------------- ##

ACLOCAL='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/scripts/missing'\'' aclocal-1.16'
AMDEPBACKSLASH='\'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMTAR='$${TAR-tar}'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='1'
AM_V='$(V)'
AR='ar'
AUTOCONF='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/scripts/missing'\'' autoconf'
AUTOHEADER='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/scripts/missing'\'' autoheader'
AUTOMAKE='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/scripts/missing'\'' automake-1.16'
AWK='mawk'
CC='gcc'
CCDEPMODE='depmode=gcc3'
CFLAGS='-Wall -W -Wno-unused-parameter -Wno-format-zero-length -pedantic -std=c99 -g -O2 -pthread'
CPP='gcc -E'
CPPFLAGS='-D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE -DCONFDIR=${prefix}/etc'
CSCOPE='cscope'
CTAGS='ctags'
CYGPATH_W='echo'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
DLLTOOL='false'
DSYMUTIL=''
DUMPBIN=''
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
EGREP='/usr/bin/grep -E'
ETAGS='etags'
EXEEXT=''
FGREP='/usr/bin/grep -F'
FILECMD='file'
GCC_FALSE='#'
GCC_TRUE=''
GCC_W_NO_FORMAT_ZERO_LENGTH='-Wno-format-zero-length'
GCC_W_NO_MISSING_FIELD_INITIALIZERS='-Wno-missing-field-initializers'
GREP='/usr/bin/grep'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
LD='/usr/bin/ld -m elf_x86_64'
LDFLAGS=''
LIBOBJS=''
LIBS=' '
LIBTOOL='$(SHELL) $(top_builddir)/libtool'
LIPO=''
LN_S='ln -s'
LTLIBOBJS=''
LT_SYS_LIBRARY_PATH=''
MAKEINFO='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/scripts/missing'\'' makeinfo'
MANIFEST_TOOL=':'
MKDIR_P='/usr/bin/mkdir -p'
NM='/usr/bin/nm -B'
NMEDIT=''
OBJDUMP='objdump'
OBJEXT='o'
OTOOL64=''
OTOOL=''
PACKAGE='slurm-drmaa'
PACKAGE_BUGREPORT='<EMAIL>'
PACKAGE_NAME='DRMAA for Slurm'
PACKAGE_STRING='DRMAA for Slurm 1.1.5'
PACKAGE_TARNAME='slurm-drmaa'
PACKAGE_URL=''
PACKAGE_VERSION='1.1.5'
PATH_SEPARATOR=':'
PTHREAD_CC='gcc'
PTHREAD_CFLAGS='-pthread'
PTHREAD_LIBS=''
RANLIB='ranlib'
SED='/usr/bin/sed'
SET_MAKE=''
SHELL='/bin/bash'
SLURM_DRMAA_MAJOR='1'
SLURM_DRMAA_MICRO='0'
SLURM_DRMAA_MINOR='2'
SLURM_DRMAA_VERSION_INFO='1:8:0'
SLURM_INCLUDES='-I/usr/include'
SLURM_LDFLAGS='-L/usr/lib'
SLURM_LIBS='-lslurm '
STRIP='strip'
VERSION='1.1.5'
ac_ct_AR='ar'
ac_ct_CC='gcc'
ac_ct_DUMPBIN=''
am__EXEEXT_FALSE=''
am__EXEEXT_TRUE='#'
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__include='include'
am__isrc=''
am__leading_dot='.'
am__nodep='_no'
am__quote=''
am__tar='$${TAR-tar} chof - "$$tardir"'
am__untar='$${TAR-tar} xf -'
ax_pthread_config=''
bindir='${exec_prefix}/bin'
build='x86_64-pc-linux-gnu'
build_alias=''
build_cpu='x86_64'
build_os='linux-gnu'
build_vendor='pc'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='${prefix}'
host='x86_64-pc-linux-gnu'
host_alias=''
host_cpu='x86_64'
host_os='linux-gnu'
host_vendor='pc'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='${SHELL} /home/<USER>/slurm-drmaa-1.1.5/scripts/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
mkdir_p='$(MKDIR_P)'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/home/<USER>/slurm-drmaa-1.1.5'
program_transform_name='s,x,x,'
psdir='${docdir}'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
subdirs=' drmaa_utils'
sysconfdir='${prefix}/etc'
target_alias=''

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "DRMAA for Slurm"
#define PACKAGE_TARNAME "slurm-drmaa"
#define PACKAGE_VERSION "1.1.5"
#define PACKAGE_STRING "DRMAA for Slurm 1.1.5"
#define PACKAGE_BUGREPORT "<EMAIL>"
#define PACKAGE_URL ""
#define PACKAGE "slurm-drmaa"
#define VERSION "1.1.5"
#define HAVE_STDIO_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_STRINGS_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_UNISTD_H 1
#define HAVE_SYS_TIME_H 1
#define STDC_HEADERS 1
#define HAVE_DLFCN_H 1
#define LT_OBJDIR ".libs/"
#define HAVE_PTHREAD_PRIO_INHERIT 1
#define HAVE_LIBSLURM 1
#define TIME_WITH_SYS_TIME 1
#define HAVE__BOOL 1
#define HAVE_STDBOOL_H 1
#define HAVE_STDDEF_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_STRINGS_H 1
#define HAVE_SYS_TIME_H 1
#define HAVE_UNISTD_H 1
#define HAVE_MALLOC 1
#define HAVE_STRFTIME 1
#define HAVE_VPRINTF 1
#define HAVE_ASPRINTF 1
#define HAVE_FSTAT 1
#define HAVE_GETCWD 1
#define HAVE_GETTIMEOFDAY 1
#define HAVE_LOCALTIME_R 1
#define HAVE_MEMSET 1
#define HAVE_MKSTEMP 1
#define HAVE_SETENV 1
#define HAVE_STRCASECMP 1
#define HAVE_STRCHR 1
#define HAVE_STRDUP 1
#define HAVE_STRERROR 1
#define HAVE_STRNDUP 1
#define HAVE_STRSTR 1
#define HAVE_STRTOL 1
#define HAVE_VASPRINTF 1

configure: exit 0
