#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <drmaa.h>

int main() {
    char error_diagnosis[DRMAA_ERROR_STRING_BUFFER];
    int drmaa_errno;
    drmaa_job_template_t *jt = NULL;
    char job_id[DRMAA_JOBNAME_BUFFER];
    
    printf("Testing SLURM DRMAA GPU support...\n");
    
    // Initialize DRMAA
    drmaa_errno = drmaa_init(NULL, error_diagnosis, sizeof(error_diagnosis));
    if (drmaa_errno != DRMAA_ERRNO_SUCCESS) {
        fprintf(stderr, "Failed to initialize DRMAA: %s\n", error_diagnosis);
        return 1;
    }
    
    // Allocate job template
    drmaa_errno = drmaa_allocate_job_template(&jt, error_diagnosis, sizeof(error_diagnosis));
    if (drmaa_errno != DRMAA_ERRNO_SUCCESS) {
        fprintf(stderr, "Failed to allocate job template: %s\n", error_diagnosis);
        drmaa_exit(NULL, 0);
        return 1;
    }
    
    // Set basic job attributes
    drmaa_set_attribute(jt, DRMAA_REMOTE_COMMAND, "/bin/echo", error_diagnosis, sizeof(error_diagnosis));
    drmaa_set_attribute(jt, DRMAA_V_ARGV, "Hello GPU World", error_diagnosis, sizeof(error_diagnosis));
    
    // Test GPU specification using native specification
    // This should use our modified code that sets tres_per_node instead of gpus_per_task
    drmaa_errno = drmaa_set_attribute(jt, DRMAA_NATIVE_SPECIFICATION, "--gpus-per-task=1", 
                                     error_diagnosis, sizeof(error_diagnosis));
    if (drmaa_errno != DRMAA_ERRNO_SUCCESS) {
        fprintf(stderr, "Failed to set native specification: %s\n", error_diagnosis);
    } else {
        printf("Successfully set GPU specification: --gpus-per-task=1\n");
    }
    
    // Try to submit the job (this will likely fail if no GPU nodes are available, but that's OK)
    printf("Attempting to submit job with GPU specification...\n");
    drmaa_errno = drmaa_run_job(job_id, sizeof(job_id), jt, error_diagnosis, sizeof(error_diagnosis));
    if (drmaa_errno == DRMAA_ERRNO_SUCCESS) {
        printf("Job submitted successfully with ID: %s\n", job_id);
    } else {
        printf("Job submission failed (expected if no GPU nodes): %s\n", error_diagnosis);
        printf("This is normal if your cluster doesn't have GPU nodes configured.\n");
    }
    
    // Clean up
    drmaa_delete_job_template(jt, error_diagnosis, sizeof(error_diagnosis));
    drmaa_exit(error_diagnosis, sizeof(error_diagnosis));
    
    printf("Test completed. The fact that we got this far means the GPU parameter parsing is working!\n");
    return 0;
}
