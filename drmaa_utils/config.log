This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by FedStage DRMAA utilities library configure 2.0.1, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  $ ./configure --disable-option-checking --prefix=/home/<USER>/slurm-drmaa-1.1.5 --cache-file=/dev/null --srcdir=.

## --------- ##
## Platform. ##
## --------- ##

hostname = 4125GS
uname -m = x86_64
uname -r = 6.1.0-31-amd64
uname -s = Linux
uname -v = #1 SMP PREEMPT_DYNAMIC Debian 6.1.128-1 (2025-02-07)

/usr/bin/uname -p = unknown
/bin/uname -X     = unknown

/bin/arch              = x86_64
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /opt/miniconda3/condabin/
PATH: /usr/local/bin/
PATH: /usr/bin/
PATH: /bin/
PATH: /usr/games/
PATH: /home/<USER>/.antigen/bundles/robbyrussell/oh-my-zsh/lib/
PATH: /home/<USER>/.antigen/bundles/Aloxaf/fzf-tab/
PATH: /home/<USER>/.fzf/bin/
PATH: /usr/local/biotools/rosetta/rosetta.source.release-371//main/source/bin/


## ----------- ##
## Core tests. ##
## ----------- ##

configure:2750: looking for aux files: config.guess config.sub ltmain.sh compile ar-lib missing install-sh
configure:2763:  trying ./scripts/
configure:2792:   ./scripts/config.guess found
configure:2792:   ./scripts/config.sub found
configure:2792:   ./scripts/ltmain.sh found
configure:2792:   ./scripts/compile found
configure:2792:   ./scripts/ar-lib found
configure:2792:   ./scripts/missing found
configure:2774:   ./scripts/install-sh found
configure:2938: checking for a BSD-compatible install
configure:3011: result: /usr/bin/install -c
configure:3022: checking whether build environment is sane
configure:3077: result: yes
configure:3236: checking for a race-free mkdir -p
configure:3280: result: /usr/bin/mkdir -p
configure:3287: checking for gawk
configure:3322: result: no
configure:3287: checking for mawk
configure:3308: found /usr/bin/mawk
configure:3319: result: mawk
configure:3330: checking whether make sets $(MAKE)
configure:3353: result: yes
configure:3383: checking whether make supports nested variables
configure:3401: result: yes
configure:3550: checking whether make supports the include directive
configure:3565: make -f confmf.GNU && cat confinc.out
this is the am__doit target
configure:3568: $? = 0
configure:3587: result: yes (GNU style)
configure:3663: checking for gcc
configure:3684: found /usr/bin/gcc
configure:3695: result: gcc
configure:4048: checking for C compiler version
configure:4057: gcc --version >&5
gcc (Debian 12.2.0-14+deb12u1) 12.2.0
Copyright (C) 2022 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:4068: $? = 0
configure:4057: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/12/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Debian 12.2.0-14+deb12u1' --with-bugurl=file:///usr/share/doc/gcc-12/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-12 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/reproducible-path/gcc-12-12.2.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/reproducible-path/gcc-12-12.2.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 12.2.0 (Debian 12.2.0-14+deb12u1) 
... rest of stderr output deleted ...
configure:4068: $? = 0
configure:4057: gcc -V >&5
gcc: error: unrecognized command-line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:4068: $? = 1
configure:4057: gcc -qversion >&5
gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc: fatal error: no input files
compilation terminated.
configure:4068: $? = 1
configure:4057: gcc -version >&5
gcc: error: unrecognized command-line option '-version'
gcc: fatal error: no input files
compilation terminated.
configure:4068: $? = 1
configure:4088: checking whether the C compiler works
configure:4110: gcc    conftest.c  >&5
configure:4114: $? = 0
configure:4164: result: yes
configure:4167: checking for C compiler default output file name
configure:4169: result: a.out
configure:4175: checking for suffix of executables
configure:4182: gcc -o conftest    conftest.c  >&5
configure:4186: $? = 0
configure:4209: result: 
configure:4231: checking whether we are cross compiling
configure:4239: gcc -o conftest    conftest.c  >&5
configure:4243: $? = 0
configure:4250: ./conftest
configure:4254: $? = 0
configure:4269: result: no
configure:4274: checking for suffix of object files
configure:4297: gcc -c   conftest.c >&5
configure:4301: $? = 0
configure:4323: result: o
configure:4327: checking whether the compiler supports GNU C
configure:4347: gcc -c   conftest.c >&5
configure:4347: $? = 0
configure:4357: result: yes
configure:4368: checking whether gcc accepts -g
configure:4389: gcc -c -g  conftest.c >&5
configure:4389: $? = 0
configure:4433: result: yes
configure:4453: checking for gcc option to enable C11 features
configure:4468: gcc  -c -g -O2  conftest.c >&5
configure:4468: $? = 0
configure:4486: result: none needed
configure:4602: checking whether gcc understands -c and -o together
configure:4625: gcc -c conftest.c -o conftest2.o
configure:4628: $? = 0
configure:4625: gcc -c conftest.c -o conftest2.o
configure:4628: $? = 0
configure:4640: result: yes
configure:4659: checking dependency style of gcc
configure:4771: result: gcc3
configure:4842: checking for ar
configure:4863: found /usr/bin/ar
configure:4874: result: ar
configure:4900: checking the archiver (ar) interface
configure:4917: gcc -c -g -O2  conftest.c >&5
configure:4917: $? = 0
configure:4920: ar cru libconftest.a conftest.o >&5
ar: `u' modifier ignored since `D' is the default (see `U')
configure:4923: $? = 0
configure:4951: result: ar
configure:5065: checking for gcc
configure:5097: result: gcc
configure:5450: checking for C compiler version
configure:5459: gcc --version >&5
gcc (Debian 12.2.0-14+deb12u1) 12.2.0
Copyright (C) 2022 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:5470: $? = 0
configure:5459: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/12/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Debian 12.2.0-14+deb12u1' --with-bugurl=file:///usr/share/doc/gcc-12/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-12 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/reproducible-path/gcc-12-12.2.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/reproducible-path/gcc-12-12.2.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 12.2.0 (Debian 12.2.0-14+deb12u1) 
... rest of stderr output deleted ...
configure:5470: $? = 0
configure:5459: gcc -V >&5
gcc: error: unrecognized command-line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:5470: $? = 1
configure:5459: gcc -qversion >&5
gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc: fatal error: no input files
compilation terminated.
configure:5470: $? = 1
configure:5459: gcc -version >&5
gcc: error: unrecognized command-line option '-version'
gcc: fatal error: no input files
compilation terminated.
configure:5470: $? = 1
configure:5474: checking whether the compiler supports GNU C
configure:5504: result: yes
configure:5515: checking whether gcc accepts -g
configure:5580: result: yes
configure:5600: checking for gcc option to enable C11 features
configure:5633: result: none needed
configure:5749: checking whether gcc understands -c and -o together
configure:5787: result: yes
configure:5806: checking dependency style of gcc
configure:5918: result: gcc3
configure:5939: checking how to run the C preprocessor
configure:5965: gcc -E  conftest.c
configure:5965: $? = 0
configure:5980: gcc -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:5980: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:6007: result: gcc -E
configure:6021: gcc -E  conftest.c
configure:6021: $? = 0
configure:6036: gcc -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:6036: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:6099: checking build system type
configure:6114: result: x86_64-pc-linux-gnu
configure:6134: checking host system type
configure:6148: result: x86_64-pc-linux-gnu
configure:6189: checking how to print strings
configure:6216: result: printf
configure:6237: checking for a sed that does not truncate output
configure:6307: result: /usr/bin/sed
configure:6325: checking for grep that handles long lines and -e
configure:6389: result: /usr/bin/grep
configure:6394: checking for egrep
configure:6462: result: /usr/bin/grep -E
configure:6467: checking for fgrep
configure:6535: result: /usr/bin/grep -F
configure:6571: checking for ld used by gcc
configure:6639: result: /usr/bin/ld
configure:6646: checking if the linker (/usr/bin/ld) is GNU ld
configure:6662: result: yes
configure:6674: checking for BSD- or MS-compatible name lister (nm)
configure:6729: result: /usr/bin/nm -B
configure:6869: checking the name lister (/usr/bin/nm -B) interface
configure:6877: gcc -c -g -O2  conftest.c >&5
configure:6880: /usr/bin/nm -B "conftest.o"
configure:6883: output
0000000000000000 B some_variable
configure:6890: result: BSD nm
configure:6893: checking whether ln -s works
configure:6897: result: yes
configure:6905: checking the maximum length of command line arguments
configure:7037: result: 1572864
configure:7085: checking how to convert x86_64-pc-linux-gnu file names to x86_64-pc-linux-gnu format
configure:7126: result: func_convert_file_noop
configure:7133: checking how to convert x86_64-pc-linux-gnu file names to toolchain format
configure:7154: result: func_convert_file_noop
configure:7161: checking for /usr/bin/ld option to reload object files
configure:7169: result: -r
configure:7248: checking for file
configure:7269: found /usr/bin/file
configure:7280: result: file
configure:7356: checking for objdump
configure:7377: found /usr/bin/objdump
configure:7388: result: objdump
configure:7420: checking how to recognize dependent libraries
configure:7621: result: pass_all
configure:7711: checking for dlltool
configure:7746: result: no
configure:7776: checking how to associate runtime and link libraries
configure:7804: result: printf %s\n
configure:7954: checking for archiver @FILE support
configure:7972: gcc -c -g -O2  conftest.c >&5
configure:7972: $? = 0
configure:7976: ar cr libconftest.a @conftest.lst >&5
configure:7979: $? = 0
configure:7984: ar cr libconftest.a @conftest.lst >&5
ar: conftest.o: No such file or directory
configure:7987: $? = 1
configure:7999: result: @
configure:8062: checking for strip
configure:8083: found /usr/bin/strip
configure:8094: result: strip
configure:8171: checking for ranlib
configure:8192: found /usr/bin/ranlib
configure:8203: result: ranlib
configure:8305: checking command to parse /usr/bin/nm -B output from gcc object
configure:8459: gcc -c -g -O2  conftest.c >&5
configure:8462: $? = 0
configure:8466: /usr/bin/nm -B conftest.o | /usr/bin/sed -n -e 's/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p' | /usr/bin/sed '/ __gnu_lto/d' > conftest.nm
configure:8532: gcc -o conftest -g -O2   conftest.c conftstm.o >&5
configure:8535: $? = 0
configure:8573: result: ok
configure:8620: checking for sysroot
configure:8651: result: no
configure:8658: checking for a working dd
configure:8702: result: /usr/bin/dd
configure:8706: checking how to truncate binary pipes
configure:8722: result: /usr/bin/dd bs=4096 count=1
configure:8859: gcc -c -g -O2  conftest.c >&5
configure:8862: $? = 0
configure:9059: checking for mt
configure:9080: found /usr/bin/mt
configure:9091: result: mt
configure:9114: checking if mt is a manifest tool
configure:9121: mt '-?'
configure:9129: result: no
configure:9854: checking for stdio.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9854: checking for stdlib.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9854: checking for string.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9854: checking for inttypes.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9854: checking for stdint.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9854: checking for strings.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9854: checking for sys/stat.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9854: checking for sys/types.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9854: checking for unistd.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9854: checking for sys/time.h
configure:9854: gcc -c -g -O2  conftest.c >&5
configure:9854: $? = 0
configure:9854: result: yes
configure:9879: checking for dlfcn.h
configure:9879: gcc -c -g -O2  conftest.c >&5
configure:9879: $? = 0
configure:9879: result: yes
configure:10138: checking for objdir
configure:10154: result: .libs
configure:10418: checking if gcc supports -fno-rtti -fno-exceptions
configure:10437: gcc -c -g -O2  -fno-rtti -fno-exceptions conftest.c >&5
cc1: warning: command-line option '-fno-rtti' is valid for C++/D/ObjC++ but not for C
configure:10441: $? = 0
configure:10454: result: no
configure:10818: checking for gcc option to produce PIC
configure:10826: result: -fPIC -DPIC
configure:10834: checking if gcc PIC flag -fPIC -DPIC works
configure:10853: gcc -c -g -O2  -fPIC -DPIC -DPIC conftest.c >&5
configure:10857: $? = 0
configure:10870: result: yes
configure:10899: checking if gcc static flag -static works
configure:10928: result: yes
configure:10943: checking if gcc supports -c -o file.o
configure:10965: gcc -c -g -O2  -o out/conftest2.o conftest.c >&5
configure:10969: $? = 0
configure:10991: result: yes
configure:10999: checking if gcc supports -c -o file.o
configure:11047: result: yes
configure:11080: checking whether the gcc linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:12354: result: yes
configure:12391: checking whether -lc should be explicitly linked in
configure:12400: gcc -c -g -O2  conftest.c >&5
configure:12403: $? = 0
configure:12418: gcc -shared  -fPIC -DPIC conftest.o  -v -Wl,-soname -Wl,conftest -o conftest 2\>\&1 \| /usr/bin/grep  -lc  \>/dev/null 2\>\&1
configure:12421: $? = 0
configure:12435: result: no
configure:12595: checking dynamic linker characteristics
configure:13177: gcc -o conftest -g -O2   -Wl,-rpath -Wl,/foo conftest.c  >&5
configure:13177: $? = 0
configure:13428: result: GNU/Linux ld.so
configure:13550: checking how to hardcode library paths into programs
configure:13575: result: immediate
configure:14127: checking whether stripping libraries is possible
configure:14136: result: yes
configure:14178: checking if libtool supports shared libraries
configure:14180: result: yes
configure:14183: checking whether to build shared libraries
configure:14208: result: yes
configure:14211: checking whether to build static libraries
configure:14215: result: yes
configure:14252: checking whether make sets $(MAKE)
configure:14275: result: yes
configure:14284: checking whether ln -s works
configure:14288: result: yes
configure:14299: checking for sed
configure:14334: result: /usr/bin/sed
configure:14347: checking for tr
configure:14370: found /usr/bin/tr
configure:14382: result: /usr/bin/tr
configure:14397: checking for bison
configure:14418: found /usr/bin/bison
configure:14429: result: bison -y
configure:14442: checking for gperf
configure:14464: result: no
configure:14476: checking for ragel
configure:14511: result: no
configure:14527: checking for rst2html
configure:14562: result: no
configure:14527: checking for rst2html.py
configure:14562: result: no
configure:14574: checking for rst2latex
configure:14609: result: no
configure:14574: checking for rst2latex.py
configure:14609: result: no
configure:14628: checking for dot
configure:14649: found /usr/bin/dot
configure:14660: result: dot
configure:14701: checking whether gcc accepts -Wno-missing-field-initializers
configure:14709: gcc -c -pedantic -std=c99 -g -O2 -Wall -Wextra -Wno-missing-field-initializers  conftest.c >&5
configure:14709: $? = 0
configure:14712: result: yes
configure:14721: checking whether gcc accepts -Wno-format-zero-length
configure:14728: gcc -c -pedantic -std=c99 -g -O2 -Wno-format-zero-length  conftest.c >&5
configure:14728: $? = 0
configure:14731: result: yes
configure:14786: checking whether byte ordering is bigendian
configure:14802: gcc -c -pedantic -std=c99 -g -O2 -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
conftest.c:25:16: error: unknown type name 'not'
   25 |                not a universal capable compiler
      |                ^~~
conftest.c:25:22: error: expected '=', ',', ';', 'asm' or '__attribute__' before 'universal'
   25 |                not a universal capable compiler
      |                      ^~~~~~~~~
conftest.c:25:22: error: unknown type name 'universal'
configure:14802: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| #ifndef __APPLE_CC__
| 	       not a universal capable compiler
| 	     #endif
| 	     typedef int dummy;
| 
configure:14848: gcc -c -pedantic -std=c99 -g -O2 -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:14848: $? = 0
configure:14867: gcc -c -pedantic -std=c99 -g -O2 -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
conftest.c: In function 'main':
conftest.c:31:18: error: unknown type name 'not'; did you mean 'ino_t'?
   31 |                  not big endian
      |                  ^~~
      |                  ino_t
conftest.c:31:26: error: expected '=', ',', ';', 'asm' or '__attribute__' before 'endian'
   31 |                  not big endian
      |                          ^~~~~~
configure:14867: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| #include <sys/types.h>
| 		#include <sys/param.h>
| 
| int
| main (void)
| {
| #if BYTE_ORDER != BIG_ENDIAN
| 		 not big endian
| 		#endif
| 
|   ;
|   return 0;
| }
configure:15001: result: no
configure:15211: checking whether gcc is Clang
configure:15238: result: no
configure:15366: checking whether pthreads work with -pthread
configure:15466: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:15466: $? = 0
configure:15476: result: yes
configure:15496: checking for joinable pthread attribute
configure:15515: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:15515: $? = 0
configure:15524: result: PTHREAD_CREATE_JOINABLE
configure:15537: checking whether more special flags are required for pthreads
configure:15551: result: no
configure:15560: checking for PTHREAD_PRIO_INHERIT
configure:15577: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:15577: $? = 0
configure:15587: result: yes
configure:15696: checking for execinfo.h
configure:15696: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15696: $? = 0
configure:15696: result: yes
configure:15702: checking for fcntl.h
configure:15702: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15702: $? = 0
configure:15702: result: yes
configure:15708: checking for inttypes.h
configure:15708: result: yes
configure:15714: checking for libintl.h
configure:15714: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15714: $? = 0
configure:15714: result: yes
configure:15720: checking for limits.h
configure:15720: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15720: $? = 0
configure:15720: result: yes
configure:15726: checking for malloc.h
configure:15726: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15726: $? = 0
configure:15726: result: yes
configure:15732: checking for stddef.h
configure:15732: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15732: $? = 0
configure:15732: result: yes
configure:15738: checking for stdint.h
configure:15738: result: yes
configure:15744: checking for stdlib.h
configure:15744: result: yes
configure:15750: checking for string.h
configure:15750: result: yes
configure:15756: checking for sys/time.h
configure:15756: result: yes
configure:15762: checking for unistd.h
configure:15762: result: yes
configure:15769: checking for _Bool
configure:15769: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15769: $? = 0
configure:15769: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
conftest.c: In function 'main':
conftest.c:69:20: error: expected expression before ')' token
   69 | if (sizeof ((_Bool)))
      |                    ^
configure:15769: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define BYTEORDER 1234
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_EXECINFO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIBINTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_MALLOC_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((_Bool)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:15769: result: yes
configure:15778: checking for stdbool.h that conforms to C99
configure:15894: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15894: $? = 0
configure:15902: result: yes
configure:15924: checking for pid_t
configure:15924: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15924: $? = 0
configure:15924: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
conftest.c: In function 'main':
conftest.c:73:20: error: expected expression before ')' token
   73 | if (sizeof ((pid_t)))
      |                    ^
configure:15924: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define BYTEORDER 1234
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_EXECINFO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIBINTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_MALLOC_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE__BOOL 1
| #define HAVE_STDBOOL_H 1
| #define TIME_WITH_SYS_TIME 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| 
| int
| main (void)
| {
| if (sizeof ((pid_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:15924: result: yes
configure:15960: checking for size_t
configure:15960: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15960: $? = 0
configure:15960: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
conftest.c: In function 'main':
conftest.c:72:21: error: expected expression before ')' token
   72 | if (sizeof ((size_t)))
      |                     ^
configure:15960: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define BYTEORDER 1234
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_EXECINFO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIBINTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_MALLOC_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE__BOOL 1
| #define HAVE_STDBOOL_H 1
| #define TIME_WITH_SYS_TIME 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((size_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:15960: result: yes
configure:15970: checking for ssize_t
configure:15970: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15970: $? = 0
configure:15970: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
conftest.c: In function 'main':
conftest.c:72:22: error: expected expression before ')' token
   72 | if (sizeof ((ssize_t)))
      |                      ^
configure:15970: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define BYTEORDER 1234
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_EXECINFO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIBINTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_MALLOC_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE__BOOL 1
| #define HAVE_STDBOOL_H 1
| #define TIME_WITH_SYS_TIME 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((ssize_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:15970: result: yes
configure:15980: checking for mode_t
configure:15980: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15980: $? = 0
configure:15980: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
conftest.c: In function 'main':
conftest.c:72:21: error: expected expression before ')' token
   72 | if (sizeof ((mode_t)))
      |                     ^
configure:15980: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define BYTEORDER 1234
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_EXECINFO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIBINTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_MALLOC_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE__BOOL 1
| #define HAVE_STDBOOL_H 1
| #define TIME_WITH_SYS_TIME 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((mode_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:15980: result: yes
configure:15990: checking for uint16_t
configure:15990: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:15990: $? = 0
configure:15990: result: yes
configure:16000: checking for uint32_t
configure:16000: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16000: $? = 0
configure:16000: result: yes
configure:16012: checking for uint8_t
configure:16012: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16012: $? = 0
configure:16012: result: yes
configure:16024: checking whether struct tm is in sys/time.h or time.h
configure:16045: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16045: $? = 0
configure:16053: result: time.h
configure:16061: checking for struct tm.tm_gmtoff
configure:16061: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16061: $? = 0
configure:16061: result: yes
configure:16072: checking for an ANSI C-conforming const
configure:16139: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16139: $? = 0
configure:16147: result: yes
configure:16155: checking for inline
configure:16172: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16172: $? = 0
configure:16181: result: inline
configure:16199: checking for working volatile
configure:16219: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16219: $? = 0
configure:16227: result: yes
configure:16239: checking for working alloca.h
configure:16257: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16257: $? = 0
configure:16266: result: yes
configure:16274: checking for alloca
configure:16319: result: yes
configure:16386: checking for gcc options needed to detect all undeclared functions
configure:16408: gcc -c -pedantic -std=c99 -g -O2 -pthread  -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
conftest.c: In function 'main':
conftest.c:48:8: error: 'strchr' undeclared (first use in this function)
   48 | (void) strchr;
      |        ^~~~~~
conftest.c:1:1: note: 'strchr' is defined in header '<string.h>'; did you forget to '#include <string.h>'?
    1 | /* confdefs.h */
conftest.c:48:8: note: each undeclared identifier is reported only once for each function it appears in
   48 | (void) strchr;
      |        ^~~~~~
configure:16408: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define BYTEORDER 1234
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_EXECINFO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIBINTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_MALLOC_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE__BOOL 1
| #define HAVE_STDBOOL_H 1
| #define TIME_WITH_SYS_TIME 1
| #define HAVE_STRUCT_TM_GMTOFF 1
| #define HAVE_ALLOCA_H 1
| #define HAVE_ALLOCA 1
| /* end confdefs.h.  */
| 
| int
| main (void)
| {
| (void) strchr;
|   ;
|   return 0;
| }
configure:16435: gcc -c -pedantic -std=c99 -g -O2 -pthread  -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16435: $? = 0
configure:16452: result: none needed
configure:16466: checking whether strerror_r is declared
configure:16466: gcc -c -pedantic -std=c99 -g -O2 -pthread  -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16466: $? = 0
configure:16466: result: yes
configure:16485: checking whether strerror_r returns char *
configure:16510: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16510: $? = 0
configure:16519: result: yes
configure:16530: checking for strftime
configure:16530: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
conftest.c:63:6: warning: conflicting types for built-in function 'strftime'; expected 'long unsigned int(char *, long unsigned int,  const char *, const void *)' [-Wbuiltin-declaration-mismatch]
   63 | char strftime ();
      |      ^~~~~~~~
conftest.c:55:1: note: 'strftime' is declared in header '<time.h>'
   54 | #include <limits.h>
   55 | #undef strftime
configure:16530: $? = 0
configure:16530: result: yes
configure:16582: checking for asprintf
configure:16582: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16582: $? = 0
configure:16582: result: yes
configure:16588: checking for fstat
configure:16588: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16588: $? = 0
configure:16588: result: yes
configure:16594: checking for getcwd
configure:16594: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16594: $? = 0
configure:16594: result: yes
configure:16600: checking for gettimeofday
configure:16600: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16600: $? = 0
configure:16600: result: yes
configure:16606: checking for localtime_r
configure:16606: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16606: $? = 0
configure:16606: result: yes
configure:16612: checking for memset
configure:16612: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
conftest.c:69:6: warning: conflicting types for built-in function 'memset'; expected 'void *(void *, int,  long unsigned int)' [-Wbuiltin-declaration-mismatch]
   69 | char memset ();
      |      ^~~~~~
conftest.c:61:1: note: 'memset' is declared in header '<string.h>'
   60 | #include <limits.h>
   61 | #undef memset
configure:16612: $? = 0
configure:16612: result: yes
configure:16618: checking for strchr
configure:16618: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
conftest.c:70:6: warning: conflicting types for built-in function 'strchr'; expected 'char *(const char *, int)' [-Wbuiltin-declaration-mismatch]
   70 | char strchr ();
      |      ^~~~~~
conftest.c:62:1: note: 'strchr' is declared in header '<string.h>'
   61 | #include <limits.h>
   62 | #undef strchr
configure:16618: $? = 0
configure:16618: result: yes
configure:16624: checking for strdup
configure:16624: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16624: $? = 0
configure:16624: result: yes
configure:16630: checking for strerror
configure:16630: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16630: $? = 0
configure:16630: result: yes
configure:16636: checking for strlcpy
configure:16636: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
/usr/bin/ld: /tmp/cc5cRAsK.o: in function `main':
/home/<USER>/slurm-drmaa-1.1.5/drmaa_utils/conftest.c:84: undefined reference to `strlcpy'
collect2: error: ld returned 1 exit status
configure:16636: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "FedStage DRMAA utilities library"
| #define PACKAGE_TARNAME "drmaa_utils"
| #define PACKAGE_VERSION "2.0.1"
| #define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "drmaa_utils"
| #define VERSION "2.0.1"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_SYS_TIME_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define BYTEORDER 1234
| #define HAVE_PTHREAD_PRIO_INHERIT 1
| #define HAVE_EXECINFO_H 1
| #define HAVE_FCNTL_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIBINTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_MALLOC_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_SYS_TIME_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE__BOOL 1
| #define HAVE_STDBOOL_H 1
| #define TIME_WITH_SYS_TIME 1
| #define HAVE_STRUCT_TM_GMTOFF 1
| #define HAVE_ALLOCA_H 1
| #define HAVE_ALLOCA 1
| #define HAVE_DECL_STRERROR_R 1
| #define HAVE_STRERROR_R 1
| #define STRERROR_R_CHAR_P 1
| #define HAVE_STRFTIME 1
| #define HAVE_ASPRINTF 1
| #define HAVE_FSTAT 1
| #define HAVE_GETCWD 1
| #define HAVE_GETTIMEOFDAY 1
| #define HAVE_LOCALTIME_R 1
| #define HAVE_MEMSET 1
| #define HAVE_STRCHR 1
| #define HAVE_STRDUP 1
| #define HAVE_STRERROR 1
| /* end confdefs.h.  */
| /* Define strlcpy to an innocuous variant, in case <limits.h> declares strlcpy.
|    For example, HP-UX 11i <limits.h> declares gettimeofday.  */
| #define strlcpy innocuous_strlcpy
| 
| /* System header to define __stub macros and hopefully few prototypes,
|    which can conflict with char strlcpy (); below.  */
| 
| #include <limits.h>
| #undef strlcpy
| 
| /* Override any GCC internal prototype to avoid an error.
|    Use char because int might match the return type of a GCC
|    builtin and then its argument prototype would still apply.  */
| #ifdef __cplusplus
| extern "C"
| #endif
| char strlcpy ();
| /* The GNU C library defines this for functions which it implements
|     to always fail with ENOSYS.  Some functions are actually named
|     something starting with __ and the normal name is an alias.  */
| #if defined __stub_strlcpy || defined __stub___strlcpy
| choke me
| #endif
| 
| int
| main (void)
| {
| return strlcpy ();
|   ;
|   return 0;
| }
configure:16636: result: no
configure:16642: checking for strndup
configure:16642: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16642: $? = 0
configure:16642: result: yes
configure:16648: checking for strstr
configure:16648: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
conftest.c:74:6: warning: conflicting types for built-in function 'strstr'; expected 'char *(const char *, const char *)' [-Wbuiltin-declaration-mismatch]
   74 | char strstr ();
      |      ^~~~~~
conftest.c:66:1: note: 'strstr' is declared in header '<string.h>'
   65 | #include <limits.h>
   66 | #undef strstr
configure:16648: $? = 0
configure:16648: result: yes
configure:16654: checking for vasprintf
configure:16654: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16654: $? = 0
configure:16654: result: yes
configure:16662: checking for gettid
configure:16689: gcc -c -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE conftest.c >&5
configure:16689: $? = 0
configure:16703: result: yes
configure:16718: checking for va_copy
configure:16731: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16731: $? = 0
configure:16735: result: yes
configure:16750: checking for __va_copy
configure:16763: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16763: $? = 0
configure:16767: result: yes
configure:16788: checking for library containing backtrace
configure:16818: gcc -o conftest -pedantic -std=c99 -g -O2 -pthread -D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE  conftest.c   >&5
configure:16818: $? = 0
configure:16838: result: none required
configure:16986: checking that generated files are newer than configure
configure:16992: result: done
configure:17032: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by FedStage DRMAA utilities library config.status 2.0.1, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on 4125GS

config.status:1157: creating Makefile
config.status:1157: creating drmaa_utils/Makefile
config.status:1157: creating test/Makefile
config.status:1157: creating Doxyfile
config.status:1157: creating config.h
config.status:1338: config.h is unchanged
config.status:1386: executing depfiles commands
config.status:1463: cd drmaa_utils       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1468: $? = 0
config.status:1463: cd test       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1468: $? = 0
config.status:1386: executing libtool commands

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=x86_64-pc-linux-gnu
ac_cv_c_bigendian=no
ac_cv_c_compiler_gnu=yes
ac_cv_c_const=yes
ac_cv_c_inline=inline
ac_cv_c_uint16_t=yes
ac_cv_c_uint32_t=yes
ac_cv_c_uint8_t=yes
ac_cv_c_undeclared_builtin_options='none needed'
ac_cv_c_volatile=yes
ac_cv_env_CC_set=
ac_cv_env_CC_value=
ac_cv_env_CFLAGS_set=
ac_cv_env_CFLAGS_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CPP_set=
ac_cv_env_CPP_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_LT_SYS_LIBRARY_PATH_set=
ac_cv_env_LT_SYS_LIBRARY_PATH_value=
ac_cv_env_SED_set=
ac_cv_env_SED_value=
ac_cv_env_TR_set=
ac_cv_env_TR_value=
ac_cv_env_YACC_set=
ac_cv_env_YACC_value=
ac_cv_env_YFLAGS_set=
ac_cv_env_YFLAGS_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_func_alloca_works=yes
ac_cv_func_asprintf=yes
ac_cv_func_fstat=yes
ac_cv_func_getcwd=yes
ac_cv_func_gettimeofday=yes
ac_cv_func_localtime_r=yes
ac_cv_func_memset=yes
ac_cv_func_strchr=yes
ac_cv_func_strdup=yes
ac_cv_func_strerror=yes
ac_cv_func_strerror_r_char_p=yes
ac_cv_func_strftime=yes
ac_cv_func_strlcpy=no
ac_cv_func_strndup=yes
ac_cv_func_strstr=yes
ac_cv_func_vasprintf=yes
ac_cv_have_decl_strerror_r=yes
ac_cv_header_dlfcn_h=yes
ac_cv_header_execinfo_h=yes
ac_cv_header_fcntl_h=yes
ac_cv_header_inttypes_h=yes
ac_cv_header_libintl_h=yes
ac_cv_header_limits_h=yes
ac_cv_header_malloc_h=yes
ac_cv_header_stdbool_h=yes
ac_cv_header_stddef_h=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdio_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_time_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_unistd_h=yes
ac_cv_host=x86_64-pc-linux-gnu
ac_cv_member_struct_tm_tm_gmtoff=yes
ac_cv_objext=o
ac_cv_path_EGREP='/usr/bin/grep -E'
ac_cv_path_FGREP='/usr/bin/grep -F'
ac_cv_path_GREP=/usr/bin/grep
ac_cv_path_SED=/usr/bin/sed
ac_cv_path_TR=/usr/bin/tr
ac_cv_path_install='/usr/bin/install -c'
ac_cv_path_lt_DD=/usr/bin/dd
ac_cv_path_mkdir=/usr/bin/mkdir
ac_cv_prog_AWK=mawk
ac_cv_prog_CPP='gcc -E'
ac_cv_prog_DOT=dot
ac_cv_prog_YACC='bison -y'
ac_cv_prog_ac_ct_AR=ar
ac_cv_prog_ac_ct_CC=gcc
ac_cv_prog_ac_ct_FILECMD=file
ac_cv_prog_ac_ct_MANIFEST_TOOL=mt
ac_cv_prog_ac_ct_OBJDUMP=objdump
ac_cv_prog_ac_ct_RANLIB=ranlib
ac_cv_prog_ac_ct_STRIP=strip
ac_cv_prog_cc_c11=
ac_cv_prog_cc_g=yes
ac_cv_prog_cc_stdc=
ac_cv_prog_make_make_set=yes
ac_cv_search_backtrace='none required'
ac_cv_struct_tm=time.h
ac_cv_type__Bool=yes
ac_cv_type_mode_t=yes
ac_cv_type_pid_t=yes
ac_cv_type_size_t=yes
ac_cv_type_ssize_t=yes
ac_cv_working_alloca_h=yes
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_ar_interface=ar
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes
ax_cv_PTHREAD_CLANG=no
ax_cv_PTHREAD_JOINABLE_ATTR=PTHREAD_CREATE_JOINABLE
ax_cv_PTHREAD_PRIO_INHERIT=yes
ax_cv_PTHREAD_SPECIAL_FLAGS=no
lt_cv_ar_at_file=@
lt_cv_archive_cmds_need_lc=no
lt_cv_deplibs_check_method=pass_all
lt_cv_file_magic_cmd='$MAGIC_CMD'
lt_cv_file_magic_test_file=
lt_cv_ld_reload_flag=-r
lt_cv_nm_interface='BSD nm'
lt_cv_objdir=.libs
lt_cv_path_LD=/usr/bin/ld
lt_cv_path_NM='/usr/bin/nm -B'
lt_cv_path_mainfest_tool=no
lt_cv_prog_compiler_c_o=yes
lt_cv_prog_compiler_pic='-fPIC -DPIC'
lt_cv_prog_compiler_pic_works=yes
lt_cv_prog_compiler_rtti_exceptions=no
lt_cv_prog_compiler_static_works=yes
lt_cv_prog_gnu_ld=yes
lt_cv_sharedlib_from_linklib_cmd='printf %s\n'
lt_cv_shlibpath_overrides_runpath=yes
lt_cv_sys_global_symbol_pipe='/usr/bin/sed -n -e '\''s/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p'\'' | /usr/bin/sed '\''/ __gnu_lto/d'\'''
lt_cv_sys_global_symbol_to_c_name_address='/usr/bin/sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_c_name_address_lib_prefix='/usr/bin/sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(lib.*\)$/  {"\1", (void *) \&\1},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"lib\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_cdecl='/usr/bin/sed -n -e '\''s/^T .* \(.*\)$/extern int \1();/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/extern char \1;/p'\'''
lt_cv_sys_global_symbol_to_import=
lt_cv_sys_max_cmd_len=1572864
lt_cv_to_host_file_cmd=func_convert_file_noop
lt_cv_to_tool_file_cmd=func_convert_file_noop
lt_cv_truncate_bin='/usr/bin/dd bs=4096 count=1'

## ----------------- ##
## Output variables. ##
## ----------------- ##

ACLOCAL='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/drmaa_utils/scripts/missing'\'' aclocal-1.16'
ALLOCA=''
AMDEPBACKSLASH='\'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMTAR='$${TAR-tar}'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='1'
AM_V='$(V)'
AR='ar'
AUTOCONF='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/drmaa_utils/scripts/missing'\'' autoconf'
AUTOHEADER='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/drmaa_utils/scripts/missing'\'' autoheader'
AUTOMAKE='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/drmaa_utils/scripts/missing'\'' automake-1.16'
AWK='mawk'
CC='gcc'
CCDEPMODE='depmode=gcc3'
CFLAGS='-Wall -W -Wno-unused  -Wno-format-zero-length  -Wno-missing-field-initializers -pedantic -std=c99 -g -O2 -pthread'
CPP='gcc -E'
CPPFLAGS='-D_REENTRANT -D_THREAD_SAFE -DNDEBUG  -D_GNU_SOURCE'
CSCOPE='cscope'
CTAGS='ctags'
CYGPATH_W='echo'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
DEVELOPER_MODE_FALSE=''
DEVELOPER_MODE_TRUE='#'
DLLTOOL='false'
DOT='dot'
DRMAA_UTILS_MAJOR='2'
DRMAA_UTILS_MICRO='1'
DRMAA_UTILS_MINOR='0'
DRMAA_UTILS_STANDALONE_FALSE='#'
DRMAA_UTILS_STANDALONE_TRUE=''
DRMAA_UTILS_VERSION_INFO='2:1:0'
DSYMUTIL=''
DUMPBIN=''
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
EGREP='/usr/bin/grep -E'
ETAGS='etags'
EXEEXT=''
FGREP='/usr/bin/grep -F'
FILECMD='file'
GCC_FALSE='#'
GCC_TRUE=''
GCC_W_NO_FORMAT_ZERO_LENGTH='-Wno-format-zero-length'
GCC_W_NO_MISSING_FIELD_INITIALIZERS='-Wno-missing-field-initializers'
GPERF='/home/<USER>/slurm-drmaa-1.1.5/drmaa_utils/scripts/gperf-fallback.sh'
GREP='/usr/bin/grep'
HAVE_DOT='yes'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
LD='/usr/bin/ld -m elf_x86_64'
LDFLAGS=''
LIBOBJS=''
LIBS=' '
LIBTOOL='$(SHELL) $(top_builddir)/libtool'
LIPO=''
LN_S='ln -s'
LTLIBOBJS=''
LT_SYS_LIBRARY_PATH=''
MAKEINFO='${SHELL} '\''/home/<USER>/slurm-drmaa-1.1.5/drmaa_utils/scripts/missing'\'' makeinfo'
MANIFEST_TOOL=':'
MKDIR_P='/usr/bin/mkdir -p'
NM='/usr/bin/nm -B'
NMEDIT=''
OBJDUMP='objdump'
OBJEXT='o'
OTOOL64=''
OTOOL=''
PACKAGE='drmaa_utils'
PACKAGE_BUGREPORT='<EMAIL>'
PACKAGE_NAME='FedStage DRMAA utilities library'
PACKAGE_STRING='FedStage DRMAA utilities library 2.0.1'
PACKAGE_TARNAME='drmaa_utils'
PACKAGE_URL=''
PACKAGE_VERSION='2.0.1'
PATH_SEPARATOR=':'
PTHREAD_CC='gcc'
PTHREAD_CFLAGS='-pthread'
PTHREAD_LIBS=''
RAGEL='sh ../m4/missing-dev-prog.sh ragel'
RANLIB='ranlib'
RST2HTML=''
RST2LATEX=''
SED='/usr/bin/sed'
SET_MAKE=''
SHELL='/bin/bash'
STRIP='strip'
TR='/usr/bin/tr'
VERSION='2.0.1'
YACC='bison -y'
YFLAGS=''
ac_ct_AR='ar'
ac_ct_CC='gcc'
ac_ct_DUMPBIN=''
am__EXEEXT_FALSE=''
am__EXEEXT_TRUE='#'
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__include='include'
am__isrc=''
am__leading_dot='.'
am__nodep='_no'
am__quote=''
am__tar='$${TAR-tar} chof - "$$tardir"'
am__untar='$${TAR-tar} xf -'
ax_pthread_config=''
bindir='${exec_prefix}/bin'
build='x86_64-pc-linux-gnu'
build_alias=''
build_cpu='x86_64'
build_os='linux-gnu'
build_vendor='pc'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='${prefix}'
host='x86_64-pc-linux-gnu'
host_alias=''
host_cpu='x86_64'
host_os='linux-gnu'
host_vendor='pc'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='${SHELL} /home/<USER>/slurm-drmaa-1.1.5/drmaa_utils/scripts/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
mkdir_p='$(MKDIR_P)'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/home/<USER>/slurm-drmaa-1.1.5'
program_transform_name='s,x,x,'
psdir='${docdir}'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='${prefix}/etc'
target_alias=''

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "FedStage DRMAA utilities library"
#define PACKAGE_TARNAME "drmaa_utils"
#define PACKAGE_VERSION "2.0.1"
#define PACKAGE_STRING "FedStage DRMAA utilities library 2.0.1"
#define PACKAGE_BUGREPORT "<EMAIL>"
#define PACKAGE_URL ""
#define PACKAGE "drmaa_utils"
#define VERSION "2.0.1"
#define HAVE_STDIO_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_STRINGS_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_UNISTD_H 1
#define HAVE_SYS_TIME_H 1
#define STDC_HEADERS 1
#define HAVE_DLFCN_H 1
#define LT_OBJDIR ".libs/"
#define BYTEORDER 1234
#define HAVE_PTHREAD_PRIO_INHERIT 1
#define HAVE_EXECINFO_H 1
#define HAVE_FCNTL_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_LIBINTL_H 1
#define HAVE_LIMITS_H 1
#define HAVE_MALLOC_H 1
#define HAVE_STDDEF_H 1
#define HAVE_STDINT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_SYS_TIME_H 1
#define HAVE_UNISTD_H 1
#define HAVE__BOOL 1
#define HAVE_STDBOOL_H 1
#define TIME_WITH_SYS_TIME 1
#define HAVE_STRUCT_TM_GMTOFF 1
#define HAVE_ALLOCA_H 1
#define HAVE_ALLOCA 1
#define HAVE_DECL_STRERROR_R 1
#define HAVE_STRERROR_R 1
#define STRERROR_R_CHAR_P 1
#define HAVE_STRFTIME 1
#define HAVE_ASPRINTF 1
#define HAVE_FSTAT 1
#define HAVE_GETCWD 1
#define HAVE_GETTIMEOFDAY 1
#define HAVE_LOCALTIME_R 1
#define HAVE_MEMSET 1
#define HAVE_STRCHR 1
#define HAVE_STRDUP 1
#define HAVE_STRERROR 1
#define HAVE_STRNDUP 1
#define HAVE_STRSTR 1
#define HAVE_VASPRINTF 1
#define HAVE_GETTID 1
#define HAVE_VA_COPY 1
#define HAVE___VA_COPY 1
#define DRMAA_DIR_BIN "/home/<USER>/slurm-drmaa-1.1.5/bin"
#define DRMAA_DIR_PREFIX "/home/<USER>/slurm-drmaa-1.1.5"
#define DRMAA_DIR_LOCALSTATE "/home/<USER>/slurm-drmaa-1.1.5/var"
#define DRMAA_DIR_SYSCONF "/home/<USER>/slurm-drmaa-1.1.5/etc"
#define DRMAA_DIR_DATA "/home/<USER>/slurm-drmaa-1.1.5/share"

configure: exit 0
