#! /bin/sh
# ylwrap - wrapper for lex/yacc invocations.

scriptversion=2018-03-07.03; # UTC

# Copyright (C) 1996-2021 Free Software Foundation, Inc.
#
# Written by <PERSON> <<EMAIL>>.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2, or (at your option)
# any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

# As a special exception to the GNU General Public License, if you
# distribute this file as part of a program that contains a
# configuration script generated by Autoconf, you may include it under
# the same distribution terms that you use for the rest of that program.

# This file is maintained in Automake, please report
# <AUTHOR> <EMAIL> or send patches to
# <<EMAIL>>.

get_dirname ()
{
  case $1 in
    */*|*\\*) printf '%s\n' "$1" | sed -e 's|\([\\/]\)[^\\/]*$|\1|';;
    # Otherwise,  we want the empty string (not ".").
  esac
}

# guard FILE
# ----------
# The CPP macro used to guard inclusion of FILE.
guard ()
{
  printf '%s\n' "$1"                                                    \
    | sed                                                               \
        -e 'y/abcdefghijklmnopqrstuvwxyz/ABCDEFGHIJKLMNOPQRSTUVWXYZ/'   \
        -e 's/[^ABCDEFGHIJKLMNOPQRSTUVWXYZ]/_/g'                        \
        -e 's/__*/_/g'
}

# quote_for_sed [STRING]
# ----------------------
# Return STRING (or stdin) quoted to be used as a sed pattern.
quote_for_sed ()
{
  case $# in
    0) cat;;
    1) printf '%s\n' "$1";;
  esac \
    | sed -e 's|[][\\.*]|\\&|g'
}

case "$1" in
  '')
    echo "$0: No files given.  Try '$0 --help' for more information." 1>&2
    exit 1
    ;;
  --basedir)
    basedir=$2
    shift 2
    ;;
  -h|--h*)
    cat <<\EOF
Usage: ylwrap [--help|--version] INPUT [OUTPUT DESIRED]... -- PROGRAM [ARGS]...

Wrapper for lex/yacc invocations, renaming files as desired.

  INPUT is the input file
  OUTPUT is one file PROG generates
  DESIRED is the file we actually want instead of OUTPUT
  PROGRAM is program to run
  ARGS are passed to PROG

Any number of OUTPUT,DESIRED pairs may be used.

Report bugs to <<EMAIL>>.
EOF
    exit $?
    ;;
  -v|--v*)
    echo "ylwrap $scriptversion"
    exit $?
    ;;
esac


# The input.
input=$1
shift
# We'll later need for a correct munging of "#line" directives.
input_sub_rx=`get_dirname "$input" | quote_for_sed`
case $input in
  [\\/]* | ?:[\\/]*)
    # Absolute path; do nothing.
    ;;
  *)
    # Relative path.  Make it absolute.
    input=`pwd`/$input
    ;;
esac
input_rx=`get_dirname "$input" | quote_for_sed`

# Since DOS filename conventions don't allow two dots,
# the DOS version of Bison writes out y_tab.c instead of y.tab.c
# and y_tab.h instead of y.tab.h. Test to see if this is the case.
y_tab_nodot=false
if test -f y_tab.c || test -f y_tab.h; then
  y_tab_nodot=true
fi

# The parser itself, the first file, is the destination of the .y.c
# rule in the Makefile.
parser=$1

# A sed program to s/FROM/TO/g for all the FROM/TO so that, for
# instance, we rename #include "y.tab.h" into #include "parse.h"
# during the conversion from y.tab.c to parse.c.
sed_fix_filenames=

# Also rename header guards, as Bison 2.7 for instance uses its header
# guard in its implementation file.
sed_fix_header_guards=

while test $# -ne 0; do
  if test x"$1" = x"--"; then
    shift
    break
  fi
  from=$1
  # Handle y_tab.c and y_tab.h output by DOS
  if $y_tab_nodot; then
    case $from in
      "y.tab.c") from=y_tab.c;;
      "y.tab.h") from=y_tab.h;;
    esac
  fi
  shift
  to=$1
  shift
  sed_fix_filenames="${sed_fix_filenames}s|"`quote_for_sed "$from"`"|$to|g;"
  sed_fix_header_guards="${sed_fix_header_guards}s|"`guard "$from"`"|"`guard "$to"`"|g;"
done

# The program to run.
prog=$1
shift
# Make any relative path in $prog absolute.
case $prog in
  [\\/]* | ?:[\\/]*) ;;
  *[\\/]*) prog=`pwd`/$prog ;;
esac

dirname=ylwrap$$
do_exit="cd '`pwd`' && rm -rf $dirname > /dev/null 2>&1;"' (exit $ret); exit $ret'
trap "ret=129; $do_exit" 1
trap "ret=130; $do_exit" 2
trap "ret=141; $do_exit" 13
trap "ret=143; $do_exit" 15
mkdir $dirname || exit 1

cd $dirname

case $# in
  0) "$prog" "$input" ;;
  *) "$prog" "$@" "$input" ;;
esac
ret=$?

if test $ret -eq 0; then
  for from in *
  do
    to=`printf '%s\n' "$from" | sed "$sed_fix_filenames"`
    if test -f "$from"; then
      # If $2 is an absolute path name, then just use that,
      # otherwise prepend '../'.
      case $to in
        [\\/]* | ?:[\\/]*) target=$to;;
        *) target=../$to;;
      esac

      # Do not overwrite unchanged header files to avoid useless
      # recompilations.  Always update the parser itself: it is the
      # destination of the .y.c rule in the Makefile.  Divert the
      # output of all other files to a temporary file so we can
      # compare them to existing versions.
      if test $from != $parser; then
        realtarget=$target
        target=tmp-`printf '%s\n' "$target" | sed 's|.*[\\/]||g'`
      fi

      # Munge "#line" or "#" directives.  Don't let the resulting
      # debug information point at an absolute srcdir.  Use the real
      # output file name, not yy.lex.c for instance.  Adjust the
      # include guards too.
      sed -e "/^#/!b"                           \
          -e "s|$input_rx|$input_sub_rx|"       \
          -e "$sed_fix_filenames"               \
          -e "$sed_fix_header_guards"           \
        "$from" >"$target" || ret=$?

      # Check whether files must be updated.
      if test "$from" != "$parser"; then
        if test -f "$realtarget" && cmp -s "$realtarget" "$target"; then
          echo "$to is unchanged"
          rm -f "$target"
        else
          echo "updating $to"
          mv -f "$target" "$realtarget"
        fi
      fi
    else
      # A missing file is only an error for the parser.  This is a
      # blatant hack to let us support using "yacc -d".  If -d is not
      # specified, don't fail when the header file is "missing".
      if test "$from" = "$parser"; then
        ret=1
      fi
    fi
  done
fi

# Remove the directory.
cd ..
rm -rf $dirname

exit $ret

# Local Variables:
# mode: shell-script
# sh-indentation: 2
# eval: (add-hook 'before-save-hook 'time-stamp)
# time-stamp-start: "scriptversion="
# time-stamp-format: "%:y-%02m-%02d.%02H"
# time-stamp-time-zone: "UTC0"
# time-stamp-end: "; # UTC"
# End:
