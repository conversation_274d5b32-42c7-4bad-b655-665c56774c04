# $Id$
#
# SYNOPSIS
#
#   AX_GPERF([ACTION-IF-FOUND[, [ACTION-IF-NOT-FOUND]])
#
# DESCRIPTION
#
#   Test for Gperf perfect hash function generator binary.
#   When not found GPERF is set with location of fallback
#   script which prints error message and exits with non-zero
#   error code.
#
#   This macro calls::
#
#     AC_SUBST(GPERF)
#
# LAST MODIFICATION
#
#   2007-12-14
#
# LICENSE
#
#   Written by <PERSON><PERSON><PERSON> <<EMAIL>>
#   and placed under Public Domain
#

AC_DEFUN([AX_GPERF], [
	AC_MSG_CHECKING([for gperf])
	if { echo a; echo b; echo c; } | gperf >/dev/null 2>&1; then
		ax_prog_gperf_ok=yes
		GPERF=gperf
	else
        abs_srcdir=`readlink -f "$srcdir"`
		GPERF="${abs_srcdir}/scripts/gperf-fallback.sh"
		cat >$GPERF <<EOF
#!/bin/sh
cat >&2 <<MESSAGE
 * ERROR: gperf was not found at configuration time while some sources are
 * build by it.  Either install gperf <http://www.gnu.org/software/gperf/>
 * or download tarball with generated sources included (than you will
 * not be able to modify .gperf files).
MESSAGE
exit 1
EOF
		chmod +x $GPERF
		ax_prog_gperf_ok=no
	fi
	AC_SUBST(GPERF)
	AC_MSG_RESULT([$ax_prog_gperf_ok])
	if test x$ax_prog_gperf_ok = xyes; then
		ifelse([$1], , :, [$1])
	else
		ifelse([$2], , :, [$2])
	fi
])
