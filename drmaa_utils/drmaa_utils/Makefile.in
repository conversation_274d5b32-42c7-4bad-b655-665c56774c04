# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

# $Id$
#
# FedStage DRMAA utilities library
# Copyright (C) 2006-2008  FedStage Systems
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
#



VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@

# allow `%m` in printf-like format string
# Bison and Ragel generated code have many unused symbols
@DEVELOPER_MODE_FALSE@@GCC_TRUE@am__append_1 = -Wno-unused
bin_PROGRAMS = drmaa-run$(EXEEXT) drmaa-run-bulk$(EXEEXT) \
	drmaa-job-ps$(EXEEXT) hpc-bash$(EXEEXT)
subdir = drmaa_utils
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_docutils.m4 \
	$(top_srcdir)/m4/ax_func_gettid.m4 \
	$(top_srcdir)/m4/ax_func_va_copy.m4 \
	$(top_srcdir)/m4/ax_gcc_warnings.m4 \
	$(top_srcdir)/m4/ax_gperf.m4 $(top_srcdir)/m4/ax_pthread.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(include_HEADERS) \
	$(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(bindir)" "$(DESTDIR)$(includedir)"
PROGRAMS = $(bin_PROGRAMS)
LTLIBRARIES = $(noinst_LTLIBRARIES)
libdrmaa_utils_la_LIBADD =
am__objects_1 = libdrmaa_utils_la-compat.lo libdrmaa_utils_la-conf.lo \
	libdrmaa_utils_la-conf_tab.lo libdrmaa_utils_la-datetime.lo \
	libdrmaa_utils_la-datetime_tab.lo \
	libdrmaa_utils_la-drmaa_attrib.lo libdrmaa_utils_la-environ.lo \
	libdrmaa_utils_la-exception.lo libdrmaa_utils_la-iter.lo \
	libdrmaa_utils_la-fsd_job.lo libdrmaa_utils_la-logging.lo \
	libdrmaa_utils_la-lookup3.lo libdrmaa_utils_la-template.lo \
	libdrmaa_utils_la-timedelta.lo libdrmaa_utils_la-thread.lo \
	libdrmaa_utils_la-fsd_util.lo libdrmaa_utils_la-drmaa_util.lo \
	libdrmaa_utils_la-xmalloc.lo libdrmaa_utils_la-exec.lo
am_libdrmaa_utils_la_OBJECTS = $(am__objects_1) \
	libdrmaa_utils_la-fsd_session.lo \
	libdrmaa_utils_la-drmaa_base.lo
libdrmaa_utils_la_OBJECTS = $(am_libdrmaa_utils_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
libdrmaa_utils_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libdrmaa_utils_la_LDFLAGS) $(LDFLAGS) \
	-o $@
am__objects_2 = compat.$(OBJEXT) conf.$(OBJEXT) conf_tab.$(OBJEXT) \
	datetime.$(OBJEXT) datetime_tab.$(OBJEXT) \
	drmaa_attrib.$(OBJEXT) environ.$(OBJEXT) exception.$(OBJEXT) \
	iter.$(OBJEXT) fsd_job.$(OBJEXT) logging.$(OBJEXT) \
	lookup3.$(OBJEXT) template.$(OBJEXT) timedelta.$(OBJEXT) \
	thread.$(OBJEXT) fsd_util.$(OBJEXT) drmaa_util.$(OBJEXT) \
	xmalloc.$(OBJEXT) exec.$(OBJEXT)
am_drmaa_job_ps_OBJECTS = $(am__objects_2) drmaa_job_ps.$(OBJEXT)
drmaa_job_ps_OBJECTS = $(am_drmaa_job_ps_OBJECTS)
drmaa_job_ps_LDADD = $(LDADD)
drmaa_job_ps_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(drmaa_job_ps_LDFLAGS) $(LDFLAGS) -o $@
am_drmaa_run_OBJECTS = $(am__objects_2) drmaa_run.$(OBJEXT)
drmaa_run_OBJECTS = $(am_drmaa_run_OBJECTS)
drmaa_run_LDADD = $(LDADD)
drmaa_run_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(drmaa_run_LDFLAGS) $(LDFLAGS) -o $@
am_drmaa_run_bulk_OBJECTS = $(am__objects_2) drmaa_run_bulk.$(OBJEXT)
drmaa_run_bulk_OBJECTS = $(am_drmaa_run_bulk_OBJECTS)
drmaa_run_bulk_LDADD = $(LDADD)
drmaa_run_bulk_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(drmaa_run_bulk_LDFLAGS) $(LDFLAGS) -o \
	$@
am_hpc_bash_OBJECTS = $(am__objects_2) hpc_bash.$(OBJEXT)
hpc_bash_OBJECTS = $(am_hpc_bash_OBJECTS)
hpc_bash_LDADD = $(LDADD)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/scripts/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/compat.Po ./$(DEPDIR)/conf.Po \
	./$(DEPDIR)/conf_tab.Po ./$(DEPDIR)/datetime.Po \
	./$(DEPDIR)/datetime_tab.Po ./$(DEPDIR)/drmaa_attrib.Po \
	./$(DEPDIR)/drmaa_job_ps.Po ./$(DEPDIR)/drmaa_run.Po \
	./$(DEPDIR)/drmaa_run_bulk.Po ./$(DEPDIR)/drmaa_util.Po \
	./$(DEPDIR)/environ.Po ./$(DEPDIR)/exception.Po \
	./$(DEPDIR)/exec.Po ./$(DEPDIR)/fsd_job.Po \
	./$(DEPDIR)/fsd_util.Po ./$(DEPDIR)/hpc_bash.Po \
	./$(DEPDIR)/iter.Po ./$(DEPDIR)/libdrmaa_utils_la-compat.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-conf.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-conf_tab.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-datetime.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-datetime_tab.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-drmaa_attrib.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-drmaa_base.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-drmaa_util.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-environ.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-exception.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-exec.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-fsd_job.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-fsd_session.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-fsd_util.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-iter.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-logging.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-lookup3.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-template.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-thread.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-timedelta.Plo \
	./$(DEPDIR)/libdrmaa_utils_la-xmalloc.Plo \
	./$(DEPDIR)/logging.Po ./$(DEPDIR)/lookup3.Po \
	./$(DEPDIR)/template.Po ./$(DEPDIR)/thread.Po \
	./$(DEPDIR)/timedelta.Po ./$(DEPDIR)/xmalloc.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
am__yacc_c2h = sed -e s/cc$$/hh/ -e s/cpp$$/hpp/ -e s/cxx$$/hxx/ \
		   -e s/c++$$/h++/ -e s/c$$/h/
YACCCOMPILE = $(YACC) $(AM_YFLAGS) $(YFLAGS)
LTYACCCOMPILE = $(LIBTOOL) $(AM_V_lt) $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(YACC) $(AM_YFLAGS) $(YFLAGS)
AM_V_YACC = $(am__v_YACC_@AM_V@)
am__v_YACC_ = $(am__v_YACC_@AM_DEFAULT_V@)
am__v_YACC_0 = @echo "  YACC    " $@;
am__v_YACC_1 = 
SOURCES = $(libdrmaa_utils_la_SOURCES) $(drmaa_job_ps_SOURCES) \
	$(drmaa_run_SOURCES) $(drmaa_run_bulk_SOURCES) \
	$(hpc_bash_SOURCES)
DIST_SOURCES = $(libdrmaa_utils_la_SOURCES) $(drmaa_job_ps_SOURCES) \
	$(drmaa_run_SOURCES) $(drmaa_run_bulk_SOURCES) \
	$(hpc_bash_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
HEADERS = $(include_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/scripts/depcomp \
	$(top_srcdir)/scripts/ylwrap conf_tab.c conf_tab.h \
	datetime_tab.c datetime_tab.h
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
ALLOCA = @ALLOCA@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DOT = @DOT@
DRMAA_UTILS_MAJOR = @DRMAA_UTILS_MAJOR@
DRMAA_UTILS_MICRO = @DRMAA_UTILS_MICRO@
DRMAA_UTILS_MINOR = @DRMAA_UTILS_MINOR@
DRMAA_UTILS_VERSION_INFO = @DRMAA_UTILS_VERSION_INFO@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
FILECMD = @FILECMD@
GCC_W_NO_FORMAT_ZERO_LENGTH = @GCC_W_NO_FORMAT_ZERO_LENGTH@
GCC_W_NO_MISSING_FIELD_INITIALIZERS = @GCC_W_NO_MISSING_FIELD_INITIALIZERS@
GPERF = @GPERF@
GREP = @GREP@
HAVE_DOT = @HAVE_DOT@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PTHREAD_CC = @PTHREAD_CC@
PTHREAD_CFLAGS = @PTHREAD_CFLAGS@
PTHREAD_LIBS = @PTHREAD_LIBS@
RAGEL = @RAGEL@
RANLIB = @RANLIB@
RST2HTML = @RST2HTML@
RST2LATEX = @RST2LATEX@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
TR = @TR@
VERSION = @VERSION@
YACC = @YACC@
YFLAGS = @YFLAGS@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
ax_pthread_config = @ax_pthread_config@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
GPERFFLAGS = --readonly-tables
RAGELFLAGS = 
YLWRAP = $(top_srcdir)/m4/bison_ylwrap.sh
AM_YFLAGS = -d
noinst_LTLIBRARIES = libdrmaa_utils.la
COMMON_SOURCES = \
 compat.c compat.h \
 common.h \
 conf.c conf.h \
 conf_impl.h conf_tab.y \
 datetime.c datetime.h \
 datetime_impl.h datetime_tab.y \
 drmaa_attrib.c drmaa_attrib.h \
 environ.c environ.h \
 exception.c exception.h \
 iter.c iter.h \
 fsd_job.c job.h \
 logging.c logging.h \
 lookup3.c lookup3.h \
 template.c template.h \
 timedelta.c \
 thread.c thread.h \
 fsd_util.c util.h \
 drmaa_util.c drmaa_util.h \
 xmalloc.c xmalloc.h \
 exec.c exec.h

libdrmaa_utils_la_SOURCES = $(COMMON_SOURCES) \
 fsd_session.c session.h \
 drmaa_base.c drmaa_base.h

libdrmaa_utils_la_LDFLAGS = -static
libdrmaa_utils_la_CPPFLAGS = -fPIC $(am__append_1)
include_HEADERS = drmaa.h
BUILT_SOURCES = \
 datetime_tab.c datetime_tab.h \
 conf_tab.c conf_tab.h \
 drmaa_attrib.c \
 timedelta.c

EXTRA_DIST = drmaa_attrib.gperf timedelta.rl
@DEVELOPER_MODE_TRUE@CLEANFILES = $(BUILT_SOURCES)
drmaa_run_SOURCES = $(COMMON_SOURCES) \
 drmaa_run.c

drmaa_run_bulk_SOURCES = $(COMMON_SOURCES) \
 drmaa_run_bulk.c

drmaa_job_ps_SOURCES = $(COMMON_SOURCES) \
 drmaa_job_ps.c

drmaa_run_LDFLAGS = -ldl
drmaa_run_bulk_LDFLAGS = -ldl
drmaa_job_ps_LDFLAGS = -ldl
hpc_bash_SOURCES = $(COMMON_SOURCES) \
 hpc_bash.c

all: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj .y
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign drmaa_utils/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign drmaa_utils/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
conf_tab.h: conf_tab.c
	@if test ! -f $@; then rm -f conf_tab.c; else :; fi
	@if test ! -f $@; then $(MAKE) $(AM_MAKEFLAGS) conf_tab.c; else :; fi
datetime_tab.h: datetime_tab.c
	@if test ! -f $@; then rm -f datetime_tab.c; else :; fi
	@if test ! -f $@; then $(MAKE) $(AM_MAKEFLAGS) datetime_tab.c; else :; fi

libdrmaa_utils.la: $(libdrmaa_utils_la_OBJECTS) $(libdrmaa_utils_la_DEPENDENCIES) $(EXTRA_libdrmaa_utils_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libdrmaa_utils_la_LINK)  $(libdrmaa_utils_la_OBJECTS) $(libdrmaa_utils_la_LIBADD) $(LIBS)

drmaa-job-ps$(EXEEXT): $(drmaa_job_ps_OBJECTS) $(drmaa_job_ps_DEPENDENCIES) $(EXTRA_drmaa_job_ps_DEPENDENCIES) 
	@rm -f drmaa-job-ps$(EXEEXT)
	$(AM_V_CCLD)$(drmaa_job_ps_LINK) $(drmaa_job_ps_OBJECTS) $(drmaa_job_ps_LDADD) $(LIBS)

drmaa-run$(EXEEXT): $(drmaa_run_OBJECTS) $(drmaa_run_DEPENDENCIES) $(EXTRA_drmaa_run_DEPENDENCIES) 
	@rm -f drmaa-run$(EXEEXT)
	$(AM_V_CCLD)$(drmaa_run_LINK) $(drmaa_run_OBJECTS) $(drmaa_run_LDADD) $(LIBS)

drmaa-run-bulk$(EXEEXT): $(drmaa_run_bulk_OBJECTS) $(drmaa_run_bulk_DEPENDENCIES) $(EXTRA_drmaa_run_bulk_DEPENDENCIES) 
	@rm -f drmaa-run-bulk$(EXEEXT)
	$(AM_V_CCLD)$(drmaa_run_bulk_LINK) $(drmaa_run_bulk_OBJECTS) $(drmaa_run_bulk_LDADD) $(LIBS)

hpc-bash$(EXEEXT): $(hpc_bash_OBJECTS) $(hpc_bash_DEPENDENCIES) $(EXTRA_hpc_bash_DEPENDENCIES) 
	@rm -f hpc-bash$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(hpc_bash_OBJECTS) $(hpc_bash_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compat.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/conf.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/conf_tab.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/datetime.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/datetime_tab.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/drmaa_attrib.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/drmaa_job_ps.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/drmaa_run.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/drmaa_run_bulk.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/drmaa_util.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/environ.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/exception.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/exec.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fsd_job.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fsd_util.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/hpc_bash.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/iter.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-compat.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-conf.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-conf_tab.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-datetime.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-datetime_tab.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-drmaa_attrib.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-drmaa_base.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-drmaa_util.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-environ.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-exception.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-exec.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-fsd_job.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-fsd_session.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-fsd_util.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-iter.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-logging.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-lookup3.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-template.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-thread.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-timedelta.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libdrmaa_utils_la-xmalloc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/logging.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lookup3.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/template.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/thread.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/timedelta.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/xmalloc.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LTCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

libdrmaa_utils_la-compat.lo: compat.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-compat.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-compat.Tpo -c -o libdrmaa_utils_la-compat.lo `test -f 'compat.c' || echo '$(srcdir)/'`compat.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-compat.Tpo $(DEPDIR)/libdrmaa_utils_la-compat.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='compat.c' object='libdrmaa_utils_la-compat.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-compat.lo `test -f 'compat.c' || echo '$(srcdir)/'`compat.c

libdrmaa_utils_la-conf.lo: conf.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-conf.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-conf.Tpo -c -o libdrmaa_utils_la-conf.lo `test -f 'conf.c' || echo '$(srcdir)/'`conf.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-conf.Tpo $(DEPDIR)/libdrmaa_utils_la-conf.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='conf.c' object='libdrmaa_utils_la-conf.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-conf.lo `test -f 'conf.c' || echo '$(srcdir)/'`conf.c

libdrmaa_utils_la-conf_tab.lo: conf_tab.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-conf_tab.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-conf_tab.Tpo -c -o libdrmaa_utils_la-conf_tab.lo `test -f 'conf_tab.c' || echo '$(srcdir)/'`conf_tab.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-conf_tab.Tpo $(DEPDIR)/libdrmaa_utils_la-conf_tab.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='conf_tab.c' object='libdrmaa_utils_la-conf_tab.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-conf_tab.lo `test -f 'conf_tab.c' || echo '$(srcdir)/'`conf_tab.c

libdrmaa_utils_la-datetime.lo: datetime.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-datetime.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-datetime.Tpo -c -o libdrmaa_utils_la-datetime.lo `test -f 'datetime.c' || echo '$(srcdir)/'`datetime.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-datetime.Tpo $(DEPDIR)/libdrmaa_utils_la-datetime.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datetime.c' object='libdrmaa_utils_la-datetime.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-datetime.lo `test -f 'datetime.c' || echo '$(srcdir)/'`datetime.c

libdrmaa_utils_la-datetime_tab.lo: datetime_tab.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-datetime_tab.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-datetime_tab.Tpo -c -o libdrmaa_utils_la-datetime_tab.lo `test -f 'datetime_tab.c' || echo '$(srcdir)/'`datetime_tab.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-datetime_tab.Tpo $(DEPDIR)/libdrmaa_utils_la-datetime_tab.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='datetime_tab.c' object='libdrmaa_utils_la-datetime_tab.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-datetime_tab.lo `test -f 'datetime_tab.c' || echo '$(srcdir)/'`datetime_tab.c

libdrmaa_utils_la-drmaa_attrib.lo: drmaa_attrib.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-drmaa_attrib.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-drmaa_attrib.Tpo -c -o libdrmaa_utils_la-drmaa_attrib.lo `test -f 'drmaa_attrib.c' || echo '$(srcdir)/'`drmaa_attrib.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-drmaa_attrib.Tpo $(DEPDIR)/libdrmaa_utils_la-drmaa_attrib.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='drmaa_attrib.c' object='libdrmaa_utils_la-drmaa_attrib.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-drmaa_attrib.lo `test -f 'drmaa_attrib.c' || echo '$(srcdir)/'`drmaa_attrib.c

libdrmaa_utils_la-environ.lo: environ.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-environ.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-environ.Tpo -c -o libdrmaa_utils_la-environ.lo `test -f 'environ.c' || echo '$(srcdir)/'`environ.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-environ.Tpo $(DEPDIR)/libdrmaa_utils_la-environ.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='environ.c' object='libdrmaa_utils_la-environ.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-environ.lo `test -f 'environ.c' || echo '$(srcdir)/'`environ.c

libdrmaa_utils_la-exception.lo: exception.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-exception.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-exception.Tpo -c -o libdrmaa_utils_la-exception.lo `test -f 'exception.c' || echo '$(srcdir)/'`exception.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-exception.Tpo $(DEPDIR)/libdrmaa_utils_la-exception.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='exception.c' object='libdrmaa_utils_la-exception.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-exception.lo `test -f 'exception.c' || echo '$(srcdir)/'`exception.c

libdrmaa_utils_la-iter.lo: iter.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-iter.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-iter.Tpo -c -o libdrmaa_utils_la-iter.lo `test -f 'iter.c' || echo '$(srcdir)/'`iter.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-iter.Tpo $(DEPDIR)/libdrmaa_utils_la-iter.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='iter.c' object='libdrmaa_utils_la-iter.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-iter.lo `test -f 'iter.c' || echo '$(srcdir)/'`iter.c

libdrmaa_utils_la-fsd_job.lo: fsd_job.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-fsd_job.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-fsd_job.Tpo -c -o libdrmaa_utils_la-fsd_job.lo `test -f 'fsd_job.c' || echo '$(srcdir)/'`fsd_job.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-fsd_job.Tpo $(DEPDIR)/libdrmaa_utils_la-fsd_job.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='fsd_job.c' object='libdrmaa_utils_la-fsd_job.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-fsd_job.lo `test -f 'fsd_job.c' || echo '$(srcdir)/'`fsd_job.c

libdrmaa_utils_la-logging.lo: logging.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-logging.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-logging.Tpo -c -o libdrmaa_utils_la-logging.lo `test -f 'logging.c' || echo '$(srcdir)/'`logging.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-logging.Tpo $(DEPDIR)/libdrmaa_utils_la-logging.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='logging.c' object='libdrmaa_utils_la-logging.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-logging.lo `test -f 'logging.c' || echo '$(srcdir)/'`logging.c

libdrmaa_utils_la-lookup3.lo: lookup3.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-lookup3.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-lookup3.Tpo -c -o libdrmaa_utils_la-lookup3.lo `test -f 'lookup3.c' || echo '$(srcdir)/'`lookup3.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-lookup3.Tpo $(DEPDIR)/libdrmaa_utils_la-lookup3.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='lookup3.c' object='libdrmaa_utils_la-lookup3.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-lookup3.lo `test -f 'lookup3.c' || echo '$(srcdir)/'`lookup3.c

libdrmaa_utils_la-template.lo: template.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-template.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-template.Tpo -c -o libdrmaa_utils_la-template.lo `test -f 'template.c' || echo '$(srcdir)/'`template.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-template.Tpo $(DEPDIR)/libdrmaa_utils_la-template.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='template.c' object='libdrmaa_utils_la-template.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-template.lo `test -f 'template.c' || echo '$(srcdir)/'`template.c

libdrmaa_utils_la-timedelta.lo: timedelta.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-timedelta.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-timedelta.Tpo -c -o libdrmaa_utils_la-timedelta.lo `test -f 'timedelta.c' || echo '$(srcdir)/'`timedelta.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-timedelta.Tpo $(DEPDIR)/libdrmaa_utils_la-timedelta.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='timedelta.c' object='libdrmaa_utils_la-timedelta.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-timedelta.lo `test -f 'timedelta.c' || echo '$(srcdir)/'`timedelta.c

libdrmaa_utils_la-thread.lo: thread.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-thread.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-thread.Tpo -c -o libdrmaa_utils_la-thread.lo `test -f 'thread.c' || echo '$(srcdir)/'`thread.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-thread.Tpo $(DEPDIR)/libdrmaa_utils_la-thread.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='thread.c' object='libdrmaa_utils_la-thread.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-thread.lo `test -f 'thread.c' || echo '$(srcdir)/'`thread.c

libdrmaa_utils_la-fsd_util.lo: fsd_util.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-fsd_util.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-fsd_util.Tpo -c -o libdrmaa_utils_la-fsd_util.lo `test -f 'fsd_util.c' || echo '$(srcdir)/'`fsd_util.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-fsd_util.Tpo $(DEPDIR)/libdrmaa_utils_la-fsd_util.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='fsd_util.c' object='libdrmaa_utils_la-fsd_util.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-fsd_util.lo `test -f 'fsd_util.c' || echo '$(srcdir)/'`fsd_util.c

libdrmaa_utils_la-drmaa_util.lo: drmaa_util.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-drmaa_util.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-drmaa_util.Tpo -c -o libdrmaa_utils_la-drmaa_util.lo `test -f 'drmaa_util.c' || echo '$(srcdir)/'`drmaa_util.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-drmaa_util.Tpo $(DEPDIR)/libdrmaa_utils_la-drmaa_util.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='drmaa_util.c' object='libdrmaa_utils_la-drmaa_util.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-drmaa_util.lo `test -f 'drmaa_util.c' || echo '$(srcdir)/'`drmaa_util.c

libdrmaa_utils_la-xmalloc.lo: xmalloc.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-xmalloc.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-xmalloc.Tpo -c -o libdrmaa_utils_la-xmalloc.lo `test -f 'xmalloc.c' || echo '$(srcdir)/'`xmalloc.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-xmalloc.Tpo $(DEPDIR)/libdrmaa_utils_la-xmalloc.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='xmalloc.c' object='libdrmaa_utils_la-xmalloc.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-xmalloc.lo `test -f 'xmalloc.c' || echo '$(srcdir)/'`xmalloc.c

libdrmaa_utils_la-exec.lo: exec.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-exec.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-exec.Tpo -c -o libdrmaa_utils_la-exec.lo `test -f 'exec.c' || echo '$(srcdir)/'`exec.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-exec.Tpo $(DEPDIR)/libdrmaa_utils_la-exec.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='exec.c' object='libdrmaa_utils_la-exec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-exec.lo `test -f 'exec.c' || echo '$(srcdir)/'`exec.c

libdrmaa_utils_la-fsd_session.lo: fsd_session.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-fsd_session.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-fsd_session.Tpo -c -o libdrmaa_utils_la-fsd_session.lo `test -f 'fsd_session.c' || echo '$(srcdir)/'`fsd_session.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-fsd_session.Tpo $(DEPDIR)/libdrmaa_utils_la-fsd_session.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='fsd_session.c' object='libdrmaa_utils_la-fsd_session.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-fsd_session.lo `test -f 'fsd_session.c' || echo '$(srcdir)/'`fsd_session.c

libdrmaa_utils_la-drmaa_base.lo: drmaa_base.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libdrmaa_utils_la-drmaa_base.lo -MD -MP -MF $(DEPDIR)/libdrmaa_utils_la-drmaa_base.Tpo -c -o libdrmaa_utils_la-drmaa_base.lo `test -f 'drmaa_base.c' || echo '$(srcdir)/'`drmaa_base.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libdrmaa_utils_la-drmaa_base.Tpo $(DEPDIR)/libdrmaa_utils_la-drmaa_base.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='drmaa_base.c' object='libdrmaa_utils_la-drmaa_base.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libdrmaa_utils_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libdrmaa_utils_la-drmaa_base.lo `test -f 'drmaa_base.c' || echo '$(srcdir)/'`drmaa_base.c

.y.c:
	$(AM_V_YACC)$(am__skipyacc) $(SHELL) $(YLWRAP) $< y.tab.c $@ y.tab.h `echo $@ | $(am__yacc_c2h)` y.output $*.output -- $(YACCCOMPILE)

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-includeHEADERS: $(include_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(include_HEADERS)'; test -n "$(includedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(includedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(includedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(includedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(includedir)" || exit $$?; \
	done

uninstall-includeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(include_HEADERS)'; test -n "$(includedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(includedir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-am
all-am: Makefile $(PROGRAMS) $(LTLIBRARIES) $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(bindir)" "$(DESTDIR)$(includedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-am
install-exec: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-rm -f conf_tab.c
	-rm -f conf_tab.h
	-rm -f datetime_tab.c
	-rm -f datetime_tab.h
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic clean-libtool \
	clean-noinstLTLIBRARIES mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/compat.Po
	-rm -f ./$(DEPDIR)/conf.Po
	-rm -f ./$(DEPDIR)/conf_tab.Po
	-rm -f ./$(DEPDIR)/datetime.Po
	-rm -f ./$(DEPDIR)/datetime_tab.Po
	-rm -f ./$(DEPDIR)/drmaa_attrib.Po
	-rm -f ./$(DEPDIR)/drmaa_job_ps.Po
	-rm -f ./$(DEPDIR)/drmaa_run.Po
	-rm -f ./$(DEPDIR)/drmaa_run_bulk.Po
	-rm -f ./$(DEPDIR)/drmaa_util.Po
	-rm -f ./$(DEPDIR)/environ.Po
	-rm -f ./$(DEPDIR)/exception.Po
	-rm -f ./$(DEPDIR)/exec.Po
	-rm -f ./$(DEPDIR)/fsd_job.Po
	-rm -f ./$(DEPDIR)/fsd_util.Po
	-rm -f ./$(DEPDIR)/hpc_bash.Po
	-rm -f ./$(DEPDIR)/iter.Po
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-compat.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-conf.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-conf_tab.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-datetime.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-datetime_tab.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-drmaa_attrib.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-drmaa_base.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-drmaa_util.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-environ.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-exception.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-exec.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-fsd_job.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-fsd_session.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-fsd_util.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-iter.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-logging.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-lookup3.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-template.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-thread.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-timedelta.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-xmalloc.Plo
	-rm -f ./$(DEPDIR)/logging.Po
	-rm -f ./$(DEPDIR)/lookup3.Po
	-rm -f ./$(DEPDIR)/template.Po
	-rm -f ./$(DEPDIR)/thread.Po
	-rm -f ./$(DEPDIR)/timedelta.Po
	-rm -f ./$(DEPDIR)/xmalloc.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-includeHEADERS

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/compat.Po
	-rm -f ./$(DEPDIR)/conf.Po
	-rm -f ./$(DEPDIR)/conf_tab.Po
	-rm -f ./$(DEPDIR)/datetime.Po
	-rm -f ./$(DEPDIR)/datetime_tab.Po
	-rm -f ./$(DEPDIR)/drmaa_attrib.Po
	-rm -f ./$(DEPDIR)/drmaa_job_ps.Po
	-rm -f ./$(DEPDIR)/drmaa_run.Po
	-rm -f ./$(DEPDIR)/drmaa_run_bulk.Po
	-rm -f ./$(DEPDIR)/drmaa_util.Po
	-rm -f ./$(DEPDIR)/environ.Po
	-rm -f ./$(DEPDIR)/exception.Po
	-rm -f ./$(DEPDIR)/exec.Po
	-rm -f ./$(DEPDIR)/fsd_job.Po
	-rm -f ./$(DEPDIR)/fsd_util.Po
	-rm -f ./$(DEPDIR)/hpc_bash.Po
	-rm -f ./$(DEPDIR)/iter.Po
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-compat.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-conf.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-conf_tab.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-datetime.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-datetime_tab.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-drmaa_attrib.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-drmaa_base.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-drmaa_util.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-environ.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-exception.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-exec.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-fsd_job.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-fsd_session.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-fsd_util.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-iter.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-logging.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-lookup3.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-template.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-thread.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-timedelta.Plo
	-rm -f ./$(DEPDIR)/libdrmaa_utils_la-xmalloc.Plo
	-rm -f ./$(DEPDIR)/logging.Po
	-rm -f ./$(DEPDIR)/lookup3.Po
	-rm -f ./$(DEPDIR)/template.Po
	-rm -f ./$(DEPDIR)/thread.Po
	-rm -f ./$(DEPDIR)/timedelta.Po
	-rm -f ./$(DEPDIR)/xmalloc.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS uninstall-includeHEADERS

.MAKE: all check install install-am install-exec install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-binPROGRAMS clean-generic clean-libtool \
	clean-noinstLTLIBRARIES cscopelist-am ctags ctags-am distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-binPROGRAMS install-data \
	install-data-am install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am \
	install-includeHEADERS install-info install-info-am \
	install-man install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-binPROGRAMS uninstall-includeHEADERS

.PRECIOUS: Makefile


drmaa_attrib.c: drmaa_attrib.gperf
	$(GPERF) $(GPERFFLAGS) --output-file=drmaa_attrib.c drmaa_attrib.gperf
timedelta.c: timedelta.rl
	$(RAGEL) $(RAGELFLAGS) -o timedelta.c timedelta.rl

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
