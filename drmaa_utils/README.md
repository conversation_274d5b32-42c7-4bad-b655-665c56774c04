FedStage DRMAA Utils
====================

DRMAA Utils is a utility library used by a variety of DRMAA libraries originally developed by FedStage Systems.  [PSNC
slurm-drmaa](http://apps.man.poznan.pl/trac/slurm-drmaa) continues to rely on this library.

This Git repository is an import/clone of the `drmaa-utils` subdirectory of the [upstream drmaa-misc SVN
repository](https://apps.man.poznan.pl/svn/drmaa-misc) hosted at [PSNC](http://www.man.poznan.pl/) and is used as a
submodule by [my slurm-drmaa fork](https://github.com/natefoo/slurm-drmaa/), similar to how it was an SVN external for
PSNC slurm-drmaa.

Authors
-------

Credit belongs to the original authors of this library, including:

- <PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON>r <PERSON>

License
-------

This software is distributed under the terms of the GNU General Public License, Version 3 (GPLv3).
