# libdrmaa.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.7 Debian-2.4.7-7~deb12u1
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libdrmaa.so.1'

# Names of this library.
library_names='libdrmaa.so.1.0.8 libdrmaa.so.1 libdrmaa.so'

# The name of the static archive.
old_library='libdrmaa.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' -L/usr/lib /usr/lib/x86_64-linux-gnu/libslurm.la'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libdrmaa.
current=1
age=0
revision=8

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/slurm-drmaa-1.1.5/lib'
