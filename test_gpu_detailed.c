#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <drmaa.h>

int main() {
    char error_diagnosis[DRMAA_ERROR_STRING_BUFFER];
    int drmaa_errno;
    drmaa_job_template_t *jt = NULL;
    
    printf("Testing different GPU specifications...\n");
    
    // Initialize DRMAA
    drmaa_errno = drmaa_init(NULL, error_diagnosis, sizeof(error_diagnosis));
    if (drmaa_errno != DRMAA_ERRNO_SUCCESS) {
        fprintf(stderr, "Failed to initialize DRMAA: %s\n", error_diagnosis);
        return 1;
    }
    
    // Allocate job template
    drmaa_errno = drmaa_allocate_job_template(&jt, error_diagnosis, sizeof(error_diagnosis));
    if (drmaa_errno != DRMAA_ERRNO_SUCCESS) {
        fprintf(stderr, "Failed to allocate job template: %s\n", error_diagnosis);
        drmaa_exit(NULL, 0);
        return 1;
    }
    
    // Set basic job attributes
    drmaa_set_attribute(jt, DRMAA_REMOTE_COMMAND, "/bin/echo", error_diagnosis, sizeof(error_diagnosis));
    drmaa_set_attribute(jt, DRMAA_V_ARGV, "Test", error_diagnosis, sizeof(error_diagnosis));
    
    // Test 1: Short form GPU specification
    printf("\nTest 1: Short form GPU specification (-g 2)\n");
    drmaa_errno = drmaa_set_attribute(jt, DRMAA_NATIVE_SPECIFICATION, "-g 2", 
                                     error_diagnosis, sizeof(error_diagnosis));
    if (drmaa_errno == DRMAA_ERRNO_SUCCESS) {
        printf("✓ Successfully set: -g 2\n");
    } else {
        printf("✗ Failed to set: -g 2 (%s)\n", error_diagnosis);
    }
    
    // Test 2: Long form GPU specification
    printf("\nTest 2: Long form GPU specification (--gpus-per-task=1)\n");
    drmaa_errno = drmaa_set_attribute(jt, DRMAA_NATIVE_SPECIFICATION, "--gpus-per-task=1", 
                                     error_diagnosis, sizeof(error_diagnosis));
    if (drmaa_errno == DRMAA_ERRNO_SUCCESS) {
        printf("✓ Successfully set: --gpus-per-task=1\n");
    } else {
        printf("✗ Failed to set: --gpus-per-task=1 (%s)\n", error_diagnosis);
    }
    
    // Test 3: Combined specification
    printf("\nTest 3: Combined specification (--cpus-per-task=2 --gpus-per-task=1)\n");
    drmaa_errno = drmaa_set_attribute(jt, DRMAA_NATIVE_SPECIFICATION, "--cpus-per-task=2 --gpus-per-task=1", 
                                     error_diagnosis, sizeof(error_diagnosis));
    if (drmaa_errno == DRMAA_ERRNO_SUCCESS) {
        printf("✓ Successfully set: --cpus-per-task=2 --gpus-per-task=1\n");
    } else {
        printf("✗ Failed to set: --cpus-per-task=2 --gpus-per-task=1 (%s)\n", error_diagnosis);
    }
    
    // Clean up
    drmaa_delete_job_template(jt, error_diagnosis, sizeof(error_diagnosis));
    drmaa_exit(error_diagnosis, sizeof(error_diagnosis));
    
    printf("\nAll tests completed successfully!\n");
    printf("The GPU parameter parsing is working correctly with SLURM 22.05.8\n");
    return 0;
}
